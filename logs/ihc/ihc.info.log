2025-08-09 12:15:58.717 [main] [34mINFO [0;39m [36mcom.rs.module.devops.DevOpsServerApplication:55[0;39m - Starting DevOpsServerApplication using Java 21.0.1 on thinkbook with PID 27700 (C:\dyz\5code\rs-master-v2\devops\target\classes started by 40202 in C:\dyz\5code\rs-master-v2\devops)
2025-08-09 12:15:58.725 [main] [34mINFO [0;39m [36mcom.rs.module.devops.DevOpsServerApplication:664[0;39m - The following profiles are active: conf,dev,sdk,as
2025-08-09 12:16:00.513 [main] [34mINFO [0;39m [36mo.s.boot.web.embedded.tomcat.TomcatWebServer:108[0;39m - Tomcat initialized with port(s): 9919 (http)
2025-08-09 12:16:00.525 [main] [34mINFO [0;39m [36morg.apache.coyote.http11.Http11NioProtocol:173[0;39m - Initializing ProtocolHandler ["http-nio-9919"]
2025-08-09 12:16:00.526 [main] [34mINFO [0;39m [36morg.apache.catalina.core.StandardService:173[0;39m - Starting service [Tomcat]
2025-08-09 12:16:00.526 [main] [34mINFO [0;39m [36morg.apache.catalina.core.StandardEngine:173[0;39m - Starting Servlet engine: [Apache Tomcat/9.0.43]
2025-08-09 12:16:00.663 [main] [34mINFO [0;39m [36mo.a.c.core.ContainerBase.[Tomcat].[localhost].[/]:173[0;39m - Initializing Spring embedded WebApplicationContext
2025-08-09 12:16:00.663 [main] [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext:289[0;39m - Root WebApplicationContext: initialization completed in 1687 ms
2025-08-09 12:16:01.002 [main] [34mINFO [0;39m [36mo.s.scheduling.concurrent.ThreadPoolTaskExecutor:181[0;39m - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-09 12:16:01.268 [main] [34mINFO [0;39m [36mo.s.scheduling.concurrent.ThreadPoolTaskScheduler:181[0;39m - Initializing ExecutorService 'taskScheduler'
2025-08-09 12:16:01.283 [main] [34mINFO [0;39m [36morg.apache.coyote.http11.Http11NioProtocol:173[0;39m - Starting ProtocolHandler ["http-nio-9919"]
2025-08-09 12:16:01.328 [main] [34mINFO [0;39m [36mo.s.boot.web.embedded.tomcat.TomcatWebServer:220[0;39m - Tomcat started on port(s): 9919 (http) with context path ''
2025-08-09 12:16:01.342 [main] [34mINFO [0;39m [36mcom.rs.module.devops.DevOpsServerApplication:61[0;39m - Started DevOpsServerApplication in 3.359 seconds (JVM running for 4.94)
2025-08-09 12:16:01.345 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:26[0;39m - === DevOps配置初始化检查 ===
2025-08-09 12:16:01.345 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:29[0;39m - 运行模式: server
2025-08-09 12:16:01.346 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:30[0;39m - 是否为客户端模式: false
2025-08-09 12:16:01.346 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:31[0;39m - 是否为服务端模式: true
2025-08-09 12:16:01.346 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:35[0;39m - 扫描目录: [/release/rs-acp/, /release/acp-web/]
2025-08-09 12:16:01.346 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:36[0;39m - 文件扩展名: [.jar, .zip]
2025-08-09 12:16:01.346 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:41[0;39m - 应用重启功能启用: true
2025-08-09 12:16:01.347 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:44[0;39m - 目录与应用映射关系: {rs-acp=/release/rs-acp/, acp-web=/release/acp-web/}
2025-08-09 12:16:01.347 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:45[0;39m - 映射关系数量: 2
2025-08-09 12:16:01.347 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:48[0;39m - === 映射关系详情 ===
2025-08-09 12:16:01.347 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:50[0;39m - 目录: [rs-acp] -> 应用: [/release/rs-acp/]
2025-08-09 12:16:01.347 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:51[0;39m - 目录长度: 6, 应用名长度: 16
2025-08-09 12:16:01.347 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:50[0;39m - 目录: [acp-web] -> 应用: [/release/acp-web/]
2025-08-09 12:16:01.348 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:51[0;39m - 目录长度: 7, 应用名长度: 17
2025-08-09 12:16:01.348 [main] [34mINFO [0;39m [36mc.rs.module.devops.config.DevOpsConfigInitializer:58[0;39m - === DevOps配置初始化检查完成 ===
2025-08-09 12:17:00.015 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.TaskLockManager:51[0;39m - [CLIENT_SYNC_WITH_RESTART_1754713020015] 任务锁获取成功，任务开始执行
2025-08-09 12:17:00.018 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.FileScanJob:289[0;39m - [-1] 同步并重启任务开始
2025-08-09 12:17:00.019 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.FileScanJob:296[0;39m - [-1] 当前为服务端模式，跳过客户端同步并重启任务
2025-08-09 12:17:00.019 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.TaskLockManager:86[0;39m - [CLIENT_SYNC_WITH_RESTART_1754713020015] 任务锁释放成功，执行时间: 4ms
2025-08-09 12:17:03.320 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.TaskLockManager:51[0;39m - [RELEASE_DIR_UPLOAD_1754713023320] 任务锁获取成功，任务开始执行
2025-08-09 12:17:07.866 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.ReleaseDirUploadJob:55[0;39m - [RELEASE_DIR_UPLOAD] 当前为服务端模式，跳过任务
2025-08-09 12:17:10.055 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.TaskLockManager:86[0;39m - [RELEASE_DIR_UPLOAD_1754713023320] 任务锁释放成功，执行时间: 6735ms
2025-08-09 12:17:10.056 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.TaskLockManager:51[0;39m - [CDROM_SCAN_COPY_1754713030056] 任务锁获取成功，任务开始执行
2025-08-09 12:17:10.057 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.CdromScanCopyJob:46[0;39m - 光驱扫描复制任务开始执行
2025-08-09 12:17:10.071 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.CdromScanCopyJob:89[0;39m - [CDROM_SCAN_COPY] 扫描盘符: [E:]，目标目录: C:\release，仅当天: true，后缀过滤: [.zip]，覆盖: true
2025-08-09 12:17:10.071 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.CdromScanCopyJob:168[0;39m - [CDROM_SCAN_COPY] 任务完成：复制文件数=0，耗时=14ms
2025-08-09 12:17:10.071 [scheduling-1] [34mINFO [0;39m [36mcom.rs.module.devops.job.TaskLockManager:86[0;39m - [CDROM_SCAN_COPY_1754713030056] 任务锁释放成功，执行时间: 15ms
