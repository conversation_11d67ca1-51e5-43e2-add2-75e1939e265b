<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.3</version>
        <relativePath/>
    </parent>
    <groupId>com.rs.module</groupId>
    <artifactId>devops</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>devops</name>
    <description>devops</description>
    <properties>
        <rs.version>1.0.0</rs.version>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>

        <!-- spring 相关 -->
        <spring.framework.version>5.3.4</spring.framework.version>
        <spring.security.version>5.0.3.RELEASE</spring.security.version>
        <spring.boot.version>2.4.3</spring.boot.version>
        <spring-cloud.version>2020.0.1</spring-cloud.version>
        <spring-cloud.nacos.version>2021.1</spring-cloud.nacos.version>
        <spring.boot.validation.version>2.7.18</spring.boot.validation.version>

        <!-- web 相关 -->
        <jakarta.servlet.version>4.0.4</jakarta.servlet.version>
        <springdoc.version>1.7.0</springdoc.version>

        <!-- bsp 相关 -->
        <bsp.security.version>1.4-20240315</bsp.security.version>
        <bsp.common.version>1.3.1-20240827</bsp.common.version>
        <bsp.sdk.version>1.3.1-20240827</bsp.sdk.version>

        <!-- DB 相关 -->
        <druid.version>1.1.24</druid.version>
        <mybatis.version>3.5.17</mybatis.version>
        <mybatis.spring.version>2.0.5</mybatis.spring.version>
        <mybatis-plus.version>3.4.2</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <easy-trans.version>3.0.6</easy-trans.version>
        <redisson.version>3.41.0</redisson.version>
        <mysql.jdbc.version>8.0.11</mysql.jdbc.version>
        <dm8.jdbc.version>8.1.3.140</dm8.jdbc.version>
        <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
        <postgresql.version>42.2.18</postgresql.version>

        <!-- 定时任务相关 -->
        <xxl-job.version>2.4.0</xxl-job.version>

        <!-- 监控相关 -->
        <skywalking.version>9.2.0</skywalking.version>
        <opentracing.version>0.33.0</opentracing.version>

        <!-- 工具类相关 -->
        <aspectj.version>1.9.6</aspectj.version>
        <slf4j-api.version>1.7.30</slf4j-api.version>
        <lombok.version>1.18.36</lombok.version>
        <hutool.version>5.8.37</hutool.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <guava.version>33.4.0-jre</guava.version>
        <poi.version>4.1.2</poi.version>
        <ooxml.schemas.version>1.3</ooxml.schemas.version>
        <jackson.version>2.11.4</jackson.version>
        <fastjson.version>1.2.83</fastjson.version>
        <easyexcel.version>3.1.4</easyexcel.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <okio.version>3.3.0</okio.version>
        <knife4j.version>2.0.7</knife4j.version>
        <knife4j.gateway.version>4.0.0</knife4j.gateway.version>
        <mpj.version>1.4.13</mpj.version>
        <commons.io.version>2.5</commons.io.version>
        <commons.io.version>2.5</commons.io.version>
        <apm-toolkit.version>9.2.0</apm-toolkit.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <x-file-storage.version>2.2.1</x-file-storage.version>
        <minio.version>8.5.2</minio.version>
        <okhttp.version>4.12.0</okhttp.version>
        <commons.scxml.version>0.9</commons.scxml.version>
        <statemachine.core.version>4.0.0</statemachine.core.version>
        <beansearch.version>4.3.6</beansearch.version>
        <ibeetl.version>3.31-RELEASE</ibeetl.version>
        <jpa.version>3.4.4</jpa.version>
        <beetl.version>3.15.14.RELEASE</beetl.version>
        <validation-api.version>2.0.2</validation-api.version>
        <pdfbox.version>2.0.19</pdfbox.version>
        <jbig2-imageio.version>2.0</jbig2-imageio.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <pdfbox-imageio.version>3.0.2</pdfbox-imageio.version>
        <jai-imageio.version>1.3.1</jai-imageio.version>
        <webp-imageio.version>0.1.6</webp-imageio.version>
        <ofdrw.version>1.17.8</ofdrw.version>
        <itextpdf-kernel.version>7.1.10</itextpdf-kernel.version>
        <qrcode.version>1.0</qrcode.version>
        <common-text.version>1.9</common-text.version>
        <joda-time.version>2.10.5</joda-time.version>
        <mvel2.version>2.4.14.Final</mvel2.version>
    </properties>

    <dependencies>
        <!-- spring 相关 begin -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-framework-bom</artifactId> <!-- JDK8 版本独有：保证 Spring Framework 尽量高 -->
            <version>${spring.framework.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>${pinyin4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta.servlet.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${springdoc.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${spring.boot.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <!-- 定时任务相关 begin -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job.version}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>rs-devops-${rs.version}</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <fork>true</fork>
                    <useIncrementalCompilation>true</useIncrementalCompilation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/build/rs-devops/lib</outputDirectory>
                            <!--							<outputDirectory>${project.build.directory}/lib</outputDirectory>-->
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                    <!-- 复制rs开头的jar包到rs-lib目录 -->
                    <execution>
                        <id>copy-rs-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/build/rs-devops/rs-lib</outputDirectory>
                            <includeGroupIds>com.rs</includeGroupIds>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <outputDirectory>${project.basedir}/build/rs-devops</outputDirectory>
                    <archive>
                        <manifest>
                            <mainClass>com.rs.module.devops.DevOpsServerApplication</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${project.basedir}/build/rs-devops</directory>
                            <includes>
                                <include>**/*</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <!-- 避免font文件的二进制文件格式压缩破坏 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pkcs12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jkx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.ftl</include>
                    <include>**/*.js</include>
                    <include>**/*.yml</include>
                    <include>**/*.jar</include>
                </includes>
            </resource>
        </resources>

    </build>


</project>
