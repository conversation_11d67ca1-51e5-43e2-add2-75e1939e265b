package com.rs.module.devops.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 文件哈希计算工具类
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@Slf4j
public class FileHashUtil {
    
    /**
     * 计算文件的SHA-1哈希值
     * 
     * @param file 文件对象
     * @return SHA-1哈希值，计算失败时返回null
     */
    public static String calculateSHA1(File file) {
        return calculateHash(file, "SHA-1");
    }
    
    /**
     * 计算文件的MD5哈希值
     * 
     * @param file 文件对象
     * @return MD5哈希值，计算失败时返回null
     */
    public static String calculateMD5(File file) {
        return calculateHash(file, "MD5");
    }
    
    /**
     * 计算文件的SHA-256哈希值
     * 
     * @param file 文件对象
     * @return SHA-256哈希值，计算失败时返回null
     */
    public static String calculateSHA256(File file) {
        return calculateHash(file, "SHA-256");
    }
    
    /**
     * 计算文件的指定算法哈希值
     * 
     * @param file 文件对象
     * @param algorithm 哈希算法名称（如：SHA-1, MD5, SHA-256）
     * @return 哈希值，计算失败时返回null
     */
    public static String calculateHash(File file, String algorithm) {
        if (file == null || !file.exists() || !file.isFile()) {
            log.warn("文件不存在或不是有效文件: {}", file);
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            
            try (FileInputStream fis = new FileInputStream(file)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                
                while ((bytesRead = fis.read(buffer)) != -1) {
                    digest.update(buffer, 0, bytesRead);
                }
            }
            
            byte[] hashBytes = digest.digest();
            return bytesToHex(hashBytes);
            
        } catch (NoSuchAlgorithmException e) {
            log.error("不支持的哈希算法: {}", algorithm, e);
            return null;
        } catch (IOException e) {
            log.error("读取文件时发生IO错误: {}", file.getAbsolutePath(), e);
            return null;
        }
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
    
    /**
     * 验证文件哈希值
     * 
     * @param file 文件对象
     * @param expectedHash 期望的哈希值
     * @param algorithm 哈希算法
     * @return 是否匹配
     */
    public static boolean verifyHash(File file, String expectedHash, String algorithm) {
        if (expectedHash == null || expectedHash.trim().isEmpty()) {
            return false;
        }
        
        String actualHash = calculateHash(file, algorithm);
        return expectedHash.equalsIgnoreCase(actualHash);
    }
    
    /**
     * 格式化文件大小
     * 
     * @param bytes 字节数
     * @return 格式化后的文件大小字符串
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
