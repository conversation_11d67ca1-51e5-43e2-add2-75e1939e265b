package com.rs.module.devops.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DevOps文件同步配置类
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "devops")
public class DevOpsConfig {

    /**
     * 运行模式：client（客户端）或 server（服务端）
     * 默认为 client 模式
     */
    private String runMode = "client";

    /**
     * Jar文件扫描配置
     */
    private JarScan jarScan = new JarScan();

    /**
     * 文件同步配置
     */
    private FileSync fileSync = new FileSync();

    /**
     * 光驱扫描复制配置
     */
    private CdromCopy cdromCopy = new CdromCopy();

    /**
     * 发布目录上传配置
     */
    private ReleaseUpload releaseUpload = new ReleaseUpload();

    /**
     * 判断当前是否为客户端模式
     */
    public boolean isClientMode() {
        return "client".equalsIgnoreCase(runMode);
    }

    /**
     * 判断当前是否为服务端模式
     */
    public boolean isServerMode() {
        return "server".equalsIgnoreCase(runMode);
    }

    @Data
    public static class JarScan {
        /**
         * 扫描目录列表
         */
        private List<String> scanDirectories;

        /**
         * 是否启用递归扫描
         */
        private Boolean recursive = true;

        /**
         * 文件扩展名过滤器
         */
        private List<String> fileExtensions;

        /**
         * 排除的目录名称
         */
        private List<String> excludeDirectories;

        /**
         * 最大文件大小限制（字节），-1表示无限制
         */
        private Long maxFileSize = -1L;

        /**
         * 是否计算SHA-1哈希值
         */
        private Boolean calculateSha1 = true;

        /**
         * 是否计算MD5哈希值
         */
        private Boolean calculateMd5 = true;
    }

    @Data
    public static class FileSync {
        /**
         * 服务端地址
         */
        private String serverUrl;

        /**
         * 客户端扫描间隔（分钟）
         */
        private Integer clientScanInterval = 30;

        /**
         * 服务端扫描间隔（分钟）
         */
        private Integer serverScanInterval = 30;

        /**
         * 文件下载超时时间（秒）
         */
        private Integer downloadTimeout = 300;

        /**
         * 备份文件保留天数
         */
        private Integer backupRetentionDays = 7;

        /**
         * 是否启用客户端自动同步
         */
        private Boolean enableClientAutoSync = true;

        /**
         * 是否启用服务端自动扫描
         */
        private Boolean enableServerAutoScan = true;

        /**
         * 最大重试次数
         */
        private Integer maxRetryCount = 3;

        /**
         * 重试间隔（秒）
         */
        private Integer retryInterval = 10;
    }

    /**
     * 应用重启配置
     */
    private AppRestart appRestart = new AppRestart();

    /**
     * 定时任务配置
     */
    private ScheduledTasks scheduledTasks = new ScheduledTasks();

    @Data
    public static class AppRestart {
        /**
         * 是否启用应用重启功能
         */
        private Boolean enabled = false;

        /**
         * 是否在Docker环境中运行
         */
        private Boolean dockerMode = false;

        /**
         * Docker主机命令前缀（Docker模式下使用）
         */
        private String dockerHostCommand = "";

        /**
         * 命令执行超时时间（秒）
         */
        private Integer commandTimeout = 60;

        /**
         * 目录与应用的映射关系
         * key: 扫描目录路径（相对路径）
         * value: 应用名称（Docker容器名或服务名）
         */
        private Map<String, String> directoryAppMapping = new HashMap<>();

        /**
         * 重启命令模板，{appName} 会被替换为实际的应用名称
         */
        private String restartCommandTemplate = "docker restart {appName}";

        /**
         * 是否启用重启前延迟
         */
        private Boolean enableRestartDelay = true;

        /**
         * 重启前延迟时间（秒）
         */
        private Integer restartDelaySeconds = 5;


        /**
         * 是否记录重启日志
         */
        private Boolean enableRestartLog = true;
    }

    @Data
    public static class CdromCopy {
        private List<String> driveLetters;
        private String targetReleaseDir = "C:/release";
        private Boolean onlyToday = true;
        private List<String> datePatterns;
        private List<String> includeExtensions;
        private Boolean overwrite = true;
    }

    @Data
    public static class ReleaseUpload {
        /**
         * 要监控的发布目录，默认 C:/release
         */
        private String releaseDir = "C:/release";
        /**
         * 是否仅处理特定扩展名（为空则全部）
         */
        private List<String> includeExtensions;
        /**
         * 是否保留上传后的zip文件（仅针对zip）
         */
        private Boolean keepZip = false;
        /**
         * 上传目标目录（服务端绝对路径），默认与 releaseDir 同名
         */
        private String targetDirOnServer;
    }

    @Data
    public static class ScheduledTasks {
        /**
         * 客户端自动同步任务 cron 表达式
         * 默认每30分钟执行一次
         */
        private String clientAutoSyncCron = "0 */1 * * * ?";

        /**
         * 清理过期备份文件任务 cron 表达式
         * 默认每天凌晨2点执行
         */
        private String cleanExpiredBackupsCron = "0 0 2 * * ?";


        /**
         * 光驱扫描复制任务 cron 表达式，默认每5分钟执行
         */
        private String cdromScanCopyCron = "0 */1 * * * ?";

        /**
         * 发布目录上传任务 cron 表达式，默认每1分钟执行
         */
        private String releaseUploadCron = "0 */1 * * * ?";

        /**
         * 任务锁配置
         */
        private TaskLock taskLock = new TaskLock();

        @Data
        public static class TaskLock {
            /**
             * 是否启用任务锁统计
             */
            private Boolean enableStatistics = true;

            /**
             * 任务超时时间（毫秒），超过此时间的任务将被认为可能存在问题
             */
            private Long taskTimeoutMs = 1800000L; // 30分钟

            /**
             * 是否记录任务锁详细日志
             */
            private Boolean enableDetailedLogging = true;
        }
    }
}
