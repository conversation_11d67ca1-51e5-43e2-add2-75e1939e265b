package com.rs.module.devops.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 恢复比较请求DTO
 *
 * <AUTHOR>
 */
@ApiModel(description = "恢复比较请求DTO")
@Data
public class ResumeCompareRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户端IP地址", required = true)
    @NotBlank(message = "客户端IP地址不能为空")
    private String clientIp;

    public ResumeCompareRequestDTO() {
    }

    public ResumeCompareRequestDTO(String clientIp) {
        this.clientIp = clientIp;
    }
}
