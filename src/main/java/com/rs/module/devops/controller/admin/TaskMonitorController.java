package com.rs.module.devops.controller.admin;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.devops.job.TaskLockManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 定时任务监控控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 定时任务监控")
@RestController
@RequestMapping("/admin/devops/task-monitor")
@Slf4j
public class TaskMonitorController {

    @Autowired
    private TaskLockManager taskLockManager;

    @GetMapping("/status")
    @Operation(summary = "获取所有任务状态")
    public CommonResult<Map<String, Object>> getTaskStatus() {
        Map<String, Object> result = new HashMap<>();

        // 任务运行状态
        Map<String, Object> runningStatus = new HashMap<>();
        runningStatus.put("CLIENT_AUTO_SYNC", taskLockManager.isRunning("CLIENT_AUTO_SYNC"));
        runningStatus.put("CLEAN_EXPIRED_BACKUPS", taskLockManager.isRunning("CLEAN_EXPIRED_BACKUPS"));
        runningStatus.put("CLIENT_SCAN", taskLockManager.isRunning("CLIENT_SCAN"));
        runningStatus.put("SERVER_SCAN", taskLockManager.isRunning("SERVER_SCAN"));
        runningStatus.put("CLIENT_SYNC_WITH_RESTART", taskLockManager.isRunning("CLIENT_SYNC_WITH_RESTART"));
        runningStatus.put("SYNC_STATISTICS", taskLockManager.isRunning("SYNC_STATISTICS"));

        // 任务运行时间
        Map<String, Long> runningTime = new HashMap<>();
        runningTime.put("CLIENT_AUTO_SYNC", taskLockManager.getRunningTime("CLIENT_AUTO_SYNC"));
        runningTime.put("CLEAN_EXPIRED_BACKUPS", taskLockManager.getRunningTime("CLEAN_EXPIRED_BACKUPS"));
        runningTime.put("CLIENT_SCAN", taskLockManager.getRunningTime("CLIENT_SCAN"));
        runningTime.put("SERVER_SCAN", taskLockManager.getRunningTime("SERVER_SCAN"));
        runningTime.put("CLIENT_SYNC_WITH_RESTART", taskLockManager.getRunningTime("CLIENT_SYNC_WITH_RESTART"));
        runningTime.put("SYNC_STATISTICS", taskLockManager.getRunningTime("SYNC_STATISTICS"));

        result.put("runningStatus", runningStatus);
        result.put("runningTime", runningTime);

        return CommonResult.success(result);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取所有任务统计信息")
    public CommonResult<ConcurrentHashMap<String, TaskLockManager.TaskStatistics>> getTaskStatistics() {
        return CommonResult.success(taskLockManager.getAllTaskStatistics());
    }

    @GetMapping("/statistics/{taskName}")
    @Operation(summary = "获取指定任务统计信息")
    public CommonResult<TaskLockManager.TaskStatistics> getTaskStatistics(@PathVariable String taskName) {
        TaskLockManager.TaskStatistics statistics = taskLockManager.getTaskStatistics(taskName);
        if (statistics == null) {
            return CommonResult.error("任务不存在或未执行过");
        }
        return CommonResult.success(statistics);
    }

    @GetMapping("/running/{taskName}")
    @Operation(summary = "检查指定任务是否正在运行")
    public CommonResult<Map<String, Object>> isTaskRunning(@PathVariable String taskName) {
        Map<String, Object> result = new HashMap<>();
        result.put("taskName", taskName);
        result.put("isRunning", taskLockManager.isRunning(taskName));
        result.put("runningTime", taskLockManager.getRunningTime(taskName));

        return CommonResult.success(result);
    }

    @GetMapping("/summary")
    @Operation(summary = "获取任务执行摘要")
    public CommonResult<Map<String, Object>> getTaskSummary() {
        Map<String, Object> summary = new HashMap<>();
        ConcurrentHashMap<String, TaskLockManager.TaskStatistics> allStats = taskLockManager.getAllTaskStatistics();

        int totalTasks = allStats.size();
        long totalAttempts = 0;
        long totalSuccessful = 0;
        long totalFailed = 0;
        long totalCompleted = 0;
        long totalExecutionTime = 0;
        int runningTasks = 0;

        for (Map.Entry<String, TaskLockManager.TaskStatistics> entry : allStats.entrySet()) {
            String taskName = entry.getKey();
            TaskLockManager.TaskStatistics stats = entry.getValue();

            totalAttempts += stats.getTotalAttempts();
            totalSuccessful += stats.getSuccessfulLocks();
            totalFailed += stats.getFailedLocks();
            totalCompleted += stats.getCompletedTasks();
            totalExecutionTime += stats.getTotalExecutionTime();

            if (taskLockManager.isRunning(taskName)) {
                runningTasks++;
            }
        }

        summary.put("totalTasks", totalTasks);
        summary.put("runningTasks", runningTasks);
        summary.put("totalAttempts", totalAttempts);
        summary.put("totalSuccessful", totalSuccessful);
        summary.put("totalFailed", totalFailed);
        summary.put("totalCompleted", totalCompleted);
        summary.put("totalExecutionTime", totalExecutionTime);
        summary.put("overallSuccessRate", totalAttempts > 0 ? (double) totalSuccessful / totalAttempts * 100 : 0);
        summary.put("averageExecutionTime", totalCompleted > 0 ? (double) totalExecutionTime / totalCompleted : 0);

        return CommonResult.success(summary);
    }
}
