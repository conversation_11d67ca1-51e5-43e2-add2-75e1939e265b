package com.rs.module.devops.controller;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.devops.service.AppRestartService;
import com.rs.module.devops.service.DevOpsClientService;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import com.rs.module.devops.vo.RestartResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * DevOps客户端控制器
 *
 * <AUTHOR>
 */
@Api(tags = "DevOps文件同步 - 客户端")
@RestController
@RequestMapping("/devops/client")
@Validated
@Slf4j
public class DevOpsClientController {

    @Autowired
    private DevOpsClientService devOpsClientService;

    @Autowired
    private AppRestartService appRestartService;

    @GetMapping("/scan")
    @ApiOperation(value = "扫描本地文件")
    public CommonResult<List<FileInfoVO>> scanLocalFiles(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始扫描本地文件", clientIp);

            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();

            log.info("客户端 {} 扫描完成，文件数量: {}", clientIp, fileInfoList.size());
            return CommonResult.success(fileInfoList, "扫描完成");
        } catch (Exception e) {
            log.error("扫描本地文件时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("扫描失败: " + e.getMessage());
        }
    }

    @PostMapping("/submit")
    @ApiOperation(value = "提交扫描结果到服务端")
    public CommonResult<CompareReportVO> submitScanResult(@RequestBody List<FileInfoVO> fileInfoList,
                                                         HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 提交扫描结果，文件数量: {}", clientIp, fileInfoList.size());

            CompareReportVO compareReport = devOpsClientService.submitScanResult(fileInfoList);

            if (compareReport != null) {
                log.info("客户端 {} 对比完成: {}", clientIp, compareReport.getSummary());
                return CommonResult.success(compareReport, "对比完成");
            } else {
                return CommonResult.error("提交失败");
            }
        } catch (Exception e) {
            log.error("提交扫描结果时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("提交失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync")
    @ApiOperation(value = "执行文件同步")
    public CommonResult<Boolean> executeSync(@RequestBody CompareReportVO compareReport,
                                            HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始执行文件同步", clientIp);

            Boolean result = devOpsClientService.executeSync(compareReport);

            String message = result ? "同步成功" : "同步失败";
            log.info("客户端 {} 同步结果: {}", clientIp, message);

            return CommonResult.success(result, message);
        } catch (Exception e) {
            log.error("执行文件同步时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("同步失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-all")
    @ApiOperation(value = "一键同步（扫描+对比+同步）")
    public CommonResult<CompareReportVO> syncAll(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始一键同步", clientIp);

            // 1. 扫描本地文件
            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();
            log.info("客户端 {} 扫描完成，文件数量: {}", clientIp, fileInfoList.size());

            // 2. 提交到服务端对比
            CompareReportVO compareReport = devOpsClientService.submitScanResult(fileInfoList);
            if (compareReport == null) {
                return CommonResult.error("服务端对比失败");
            }

            log.info("客户端 {} 对比完成: {}", clientIp, compareReport.getSummary());

            // 3. 如果有差异，执行同步
            if (compareReport.getHasDifferences()) {
                Boolean syncResult = devOpsClientService.executeSync(compareReport);
                if (!syncResult) {
                    log.warn("客户端 {} 同步过程中出现部分失败", clientIp);
                }
            } else {
                log.info("客户端 {} 文件已是最新，无需同步", clientIp);
            }

            return CommonResult.success(compareReport, "一键同步完成");
        } catch (Exception e) {
            log.error("一键同步时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("一键同步失败: " + e.getMessage());
        }
    }

    @PostMapping("/download")
    @ApiOperation(value = "下载指定文件")
    public CommonResult<Boolean> downloadFile(@RequestBody FileInfoVO fileInfo,
                                             HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 下载文件: {}", clientIp, fileInfo.getRelativePath());

            Boolean result = devOpsClientService.downloadFile(fileInfo);

            String message = result ? "下载成功" : "下载失败";
            log.info("客户端 {} 下载结果: {}", clientIp, message);

            return CommonResult.success(result, message);
        } catch (Exception e) {
            log.error("下载文件时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("下载失败: " + e.getMessage());
        }
    }

    @PostMapping("/backup")
    @ApiOperation(value = "备份指定文件")
    public CommonResult<String> backupFile(@RequestBody FileInfoVO fileInfo,
                                          HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 备份文件: {}", clientIp, fileInfo.getRelativePath());

            String backupPath = devOpsClientService.backupFile(fileInfo);

            if (backupPath != null) {
                log.info("客户端 {} 备份成功: {}", clientIp, backupPath);
                return CommonResult.success(backupPath, "备份成功");
            } else {
                return CommonResult.error("备份失败");
            }
        } catch (Exception e) {
            log.error("备份文件时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("备份失败: " + e.getMessage());
        }
    }

    @PostMapping("/clean-backups")
    @ApiOperation(value = "清理过期备份文件")
    public CommonResult<Integer> cleanExpiredBackups(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始清理过期备份文件", clientIp);

            Integer cleanedCount = devOpsClientService.cleanExpiredBackups();

            log.info("客户端 {} 清理完成，共清理 {} 个文件", clientIp, cleanedCount);
            return CommonResult.success(cleanedCount, "清理完成");
        } catch (Exception e) {
            log.error("清理过期备份文件时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("清理失败: " + e.getMessage());
        }
    }

    @PostMapping("/verify")
    @ApiOperation(value = "验证文件完整性")
    public CommonResult<Boolean> verifyFileIntegrity(@RequestParam String filePath,
                                                    @RequestParam String expectedMd5,
                                                    HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 验证文件完整性: {}", clientIp, filePath);

            Boolean result = devOpsClientService.verifyFileIntegrity(filePath, expectedMd5);

            String message = result ? "验证通过" : "验证失败";
            log.info("客户端 {} 验证结果: {}", clientIp, message);

            return CommonResult.success(result, message);
        } catch (Exception e) {
            log.error("验证文件完整性时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("验证失败: " + e.getMessage());
        }
    }

    @GetMapping("/ip")
    @ApiOperation(value = "获取客户端IP地址")
    public CommonResult<String> getClientIpAddress(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            return CommonResult.success(clientIp, "获取成功");
        } catch (Exception e) {
            log.error("获取客户端IP时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-with-restart")
    @ApiOperation(value = "执行文件同步并自动重启相关应用")
    public CommonResult<RestartResultVO> executeSyncWithRestart(@RequestBody CompareReportVO compareReport,
                                                               HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始执行文件同步并自动重启相关应用", clientIp);

            RestartResultVO result = devOpsClientService.executeSyncWithRestart(compareReport);

            if (result != null) {
                log.info("客户端 {} 同步和重启完成: {}", clientIp, result.getSummary());
                return CommonResult.success(result, "同步和重启完成");
            } else {
                log.info("客户端 {} 同步完成，无应用需要重启", clientIp);
                return CommonResult.success(null, "同步完成，无应用需要重启");
            }
        } catch (Exception e) {
            log.error("执行文件同步和应用重启时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("同步和重启失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-all-with-restart")
    @ApiOperation(value = "一键同步并自动重启相关应用（扫描+对比+同步+重启）")
    public CommonResult<RestartResultVO> syncAllWithRestart(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始一键同步并自动重启相关应用", clientIp);

            // 1. 扫描本地文件
            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();
            log.info("客户端 {} 扫描完成，文件数量: {}", clientIp, fileInfoList.size());

            // 2. 提交到服务端对比
            CompareReportVO compareReport = devOpsClientService.submitScanResult(fileInfoList);
            if (compareReport == null) {
                return CommonResult.error("服务端对比失败");
            }

            log.info("客户端 {} 对比完成: {}", clientIp, compareReport.getSummary());

            // 3. 如果有差异，执行同步和重启
            if (compareReport.getHasDifferences()) {
                RestartResultVO restartResult = devOpsClientService.executeSyncWithRestart(compareReport);

                if (restartResult != null) {
                    log.info("客户端 {} 一键同步和重启完成: {}", clientIp, restartResult.getSummary());
                    return CommonResult.success(restartResult, "一键同步和重启完成");
                } else {
                    log.info("客户端 {} 一键同步完成，无应用需要重启", clientIp);
                    return CommonResult.success(null, "一键同步完成，无应用需要重启");
                }
            } else {
                log.info("客户端 {} 文件已是最新，无需同步和重启", clientIp);
                return CommonResult.success(null, "文件已是最新，无需同步和重启");
            }

        } catch (Exception e) {
            log.error("一键同步和重启时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("一键同步和重启失败: " + e.getMessage());
        }
    }

    @GetMapping("/restart-config")
    @ApiOperation(value = "获取应用重启配置信息")
    public CommonResult<Boolean> getRestartConfig(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 获取应用重启配置", clientIp);

            Boolean enabled = appRestartService.isRestartEnabled();

            return CommonResult.success(enabled, enabled ? "应用重启功能已启用" : "应用重启功能未启用");
        } catch (Exception e) {
            log.error("获取应用重启配置时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/rollback")
    @ApiOperation(value = "根据日期后缀回退文件")
    public CommonResult<List<String>> rollbackFilesByDateSuffix(@RequestParam String dateSuffix,
                                                               HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("客户端 {} 开始根据日期后缀 {} 回退文件", clientIp, dateSuffix);

            List<String> results = devOpsClientService.rollbackFilesByDateSuffix(dateSuffix);

            log.info("客户端 {} 文件回退操作完成", clientIp);
            return CommonResult.success(results, "文件回退操作完成");
        } catch (Exception e) {
            log.error("文件回退时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("文件回退失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }

        return ip;
    }
}
