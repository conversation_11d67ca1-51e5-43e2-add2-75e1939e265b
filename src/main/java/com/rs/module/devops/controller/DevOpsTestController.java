package com.rs.module.devops.controller;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.service.DevOpsClientService;
import com.rs.module.devops.service.DevOpsServerService;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DevOps测试控制器
 *
 * <AUTHOR>
 */
@Api(tags = "DevOps文件同步 - 测试接口")
@RestController
@RequestMapping("/devops/test")
@Validated
@Slf4j
public class DevOpsTestController {

    @Autowired
    private DevOpsConfig devOpsConfig;

    @Autowired
    private DevOpsClientService devOpsClientService;

    @Autowired
    private DevOpsServerService devOpsServerService;

    @GetMapping("/config")
    @ApiOperation(value = "获取配置信息")
    public CommonResult<DevOpsConfig> getConfig() {
        try {
            // 调试日志：打印目录映射配置
            Map<String, String> directoryAppMapping = devOpsConfig.getAppRestart().getDirectoryAppMapping();
            log.info("目录与应用映射关系: {}", directoryAppMapping);
            log.info("映射关系大小: {}", directoryAppMapping.size());
            if (!directoryAppMapping.isEmpty()) {
                directoryAppMapping.forEach((key, value) -> {
                    log.info("映射关系 - 目录: [{}], 应用: [{}]", key, value);
                });
            }

            return CommonResult.success(devOpsConfig, "获取配置成功");
        } catch (Exception e) {
            log.error("获取配置信息时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("获取配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/client/scan-test")
    @ApiOperation(value = "测试客户端文件扫描")
    public CommonResult<Map<String, Object>> testClientScan(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("测试客户端文件扫描，请求来自: {}", clientIp);

            long startTime = System.currentTimeMillis();
            List<FileInfoVO> fileInfoList = devOpsClientService.scanLocalFiles();
            long endTime = System.currentTimeMillis();

            Map<String, Object> result = new HashMap<>();
            result.put("clientIp", clientIp);
            result.put("fileCount", fileInfoList.size());
            result.put("scanTime", endTime - startTime);
            result.put("files", fileInfoList);

            log.info("客户端扫描测试完成，文件数量: {}，耗时: {}ms", fileInfoList.size(), endTime - startTime);
            return CommonResult.success(result, "扫描测试完成");
        } catch (Exception e) {
            log.error("测试客户端文件扫描时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("扫描测试失败: " + e.getMessage());
        }
    }

    @GetMapping("/server/scan-test")
    @ApiOperation(value = "测试服务端文件扫描")
    public CommonResult<Map<String, Object>> testServerScan(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("测试服务端文件扫描，请求来自: {}", clientIp);

            long startTime = System.currentTimeMillis();
            List<FileInfoVO> fileInfoList = devOpsServerService.scanServerFiles();
            long endTime = System.currentTimeMillis();

            Map<String, Object> result = new HashMap<>();
            result.put("requestIp", clientIp);
            result.put("fileCount", fileInfoList.size());
            result.put("scanTime", endTime - startTime);
            result.put("files", fileInfoList);

            log.info("服务端扫描测试完成，文件数量: {}，耗时: {}ms", fileInfoList.size(), endTime - startTime);
            return CommonResult.success(result, "扫描测试完成");
        } catch (Exception e) {
            log.error("测试服务端文件扫描时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("扫描测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/compare-test")
    @ApiOperation(value = "测试文件对比功能")
    public CommonResult<CompareReportVO> testCompare(@RequestBody(required = false) List<FileInfoVO> clientFiles,
                                                    HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("测试文件对比功能，请求来自: {}", clientIp);

            // 如果没有提供客户端文件，则使用客户端扫描结果
            if (clientFiles == null || clientFiles.isEmpty()) {
                clientFiles = devOpsClientService.scanLocalFiles();
                log.info("使用客户端扫描结果，文件数量: {}", clientFiles.size());
            }

            long startTime = System.currentTimeMillis();
            CompareReportVO compareReport = devOpsServerService.compareFiles(clientFiles, clientIp);
            long endTime = System.currentTimeMillis();

            compareReport.setClientIp(clientIp);
            log.info("文件对比测试完成，耗时: {}ms，{}", endTime - startTime, compareReport.getSummary());

            return CommonResult.success(compareReport, "对比测试完成");
        } catch (Exception e) {
            log.error("测试文件对比功能时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("对比测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-test")
    @ApiOperation(value = "测试完整同步流程")
    public CommonResult<Map<String, Object>> testFullSync(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("测试完整同步流程，请求来自: {}", clientIp);

            Map<String, Object> result = new HashMap<>();
            long totalStartTime = System.currentTimeMillis();

            // 1. 客户端扫描
            long scanStartTime = System.currentTimeMillis();
            List<FileInfoVO> clientFiles = devOpsClientService.scanLocalFiles();
            long scanEndTime = System.currentTimeMillis();

            result.put("clientScanTime", scanEndTime - scanStartTime);
            result.put("clientFileCount", clientFiles.size());

            // 2. 服务端对比
            long compareStartTime = System.currentTimeMillis();
            CompareReportVO compareReport = devOpsServerService.compareFiles(clientFiles, clientIp);
            long compareEndTime = System.currentTimeMillis();

            result.put("compareTime", compareEndTime - compareStartTime);
            result.put("compareReport", compareReport);

            // 3. 模拟同步（不实际执行文件操作）
            if (compareReport.getHasDifferences()) {
                result.put("needSync", true);
                result.put("filesToAdd", compareReport.getFilesToAdd().size());
                result.put("filesToUpdate", compareReport.getFilesToUpdate().size());
                result.put("filesToDelete", compareReport.getFilesToDelete().size());
            } else {
                result.put("needSync", false);
            }

            long totalEndTime = System.currentTimeMillis();
            result.put("totalTime", totalEndTime - totalStartTime);
            result.put("clientIp", clientIp);

            log.info("完整同步流程测试完成，总耗时: {}ms", totalEndTime - totalStartTime);
            return CommonResult.success(result, "同步流程测试完成");
        } catch (Exception e) {
            log.error("测试完整同步流程时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("同步流程测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/backup-test")
    @ApiOperation(value = "测试文件备份功能")
    public CommonResult<Map<String, Object>> testBackup(@RequestParam String filePath,
                                                       HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("测试文件备份功能，文件: {}，请求来自: {}", filePath, clientIp);

            // 创建测试文件信息
            FileInfoVO fileInfo = new FileInfoVO();
            fileInfo.setRelativePath(filePath);
            fileInfo.setFileName(filePath.substring(filePath.lastIndexOf("/") + 1));

            String backupPath = devOpsClientService.backupFile(fileInfo);

            Map<String, Object> result = new HashMap<>();
            result.put("originalPath", filePath);
            result.put("backupPath", backupPath);
            result.put("success", backupPath != null);
            result.put("clientIp", clientIp);

            String message = backupPath != null ? "备份测试成功" : "备份测试失败";
            log.info("文件备份测试完成: {}", message);

            return CommonResult.success(result, message);
        } catch (Exception e) {
            log.error("测试文件备份功能时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("备份测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/cleanup-test")
    @ApiOperation(value = "测试清理过期备份功能")
    public CommonResult<Map<String, Object>> testCleanup(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            log.info("测试清理过期备份功能，请求来自: {}", clientIp);

            long startTime = System.currentTimeMillis();
            Integer cleanedCount = devOpsClientService.cleanExpiredBackups();
            long endTime = System.currentTimeMillis();

            Map<String, Object> result = new HashMap<>();
            result.put("cleanedCount", cleanedCount);
            result.put("cleanTime", endTime - startTime);
            result.put("clientIp", clientIp);

            log.info("清理过期备份测试完成，清理文件数: {}，耗时: {}ms", cleanedCount, endTime - startTime);
            return CommonResult.success(result, "清理测试完成");
        } catch (Exception e) {
            log.error("测试清理过期备份功能时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("清理测试失败: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    @ApiOperation(value = "健康检查")
    public CommonResult<Map<String, Object>> healthCheck(HttpServletRequest request) {
        try {

            String clientIp = getClientIp(request);

            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            health.put("clientIp", clientIp);
            health.put("configLoaded", devOpsConfig != null);
            health.put("servicesAvailable", devOpsClientService != null && devOpsServerService != null);
            return CommonResult.success(health, "服务正常");
        } catch (Exception e) {
            log.error("健康检查时发生错误: {}", e.getMessage(), e);
            return CommonResult.error("服务异常: " + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }

        return ip;
    }
}
