package com.rs.module.devops.job;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.util.FileHashUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发布目录扫描上传任务
 *
 * 周期性扫描 C:/release（可配置），读取/维护本地 .sha1.json 记录；
 * 当发现文件 SHA1 变化或新增时，调用服务端 /devops/server/upload 接口上传，成功后更新记录。
 */
@Component
@Slf4j
public class ReleaseDirUploadJob {

    private static final String TASK_NAME = "RELEASE_DIR_UPLOAD";
    private static final String SHA1_RECORD_FILE = ".sha1.json";

    @Resource
    private TaskLockManager taskLockManager;

    @Resource
    private DevOpsConfig devOpsConfig;

    public void releaseUploadJob() {
        if (!taskLockManager.tryLock(TASK_NAME)) {
            return;
        }
        long start = System.currentTimeMillis();
        try {
            if (!isWindows()) {
                log.info("[{}] 非 Windows 环境，跳过任务", TASK_NAME);
                return;
            }
            if (devOpsConfig.isServerMode()) {
                log.info("[{}] 当前为服务端模式，跳过任务", TASK_NAME);
                return;
            }

            DevOpsConfig.ReleaseUpload cfg = devOpsConfig.getReleaseUpload();
            if (cfg == null) {
                log.warn("[{}] 未找到 devops.release-upload 配置，跳过", TASK_NAME);
                return;
            }

            // 1) 目标发布目录（本地）
            String releaseDir = Optional.ofNullable(cfg.getReleaseDir()).orElse("C:/release");
            Path releasePath = Paths.get(normalizeWinPath(releaseDir));
            if (!Files.exists(releasePath)) {
                try {
                    Files.createDirectories(releasePath);
                } catch (Exception e) {
                    log.error("[{}] 创建/访问发布目录失败: {}", TASK_NAME, releasePath, e);
                    return;
                }
            }

            // 2) 过滤后缀
            Set<String> includeExts = Optional.ofNullable(cfg.getIncludeExtensions())
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.stream().filter(StrUtil::isNotBlank)
                            .map(String::toLowerCase).collect(Collectors.toSet()))
                    .orElse(Collections.emptySet());

            // 3) 服务端地址与上传目标目录
            String serverUrl = Optional.ofNullable(devOpsConfig.getFileSync())
                    .map(DevOpsConfig.FileSync::getServerUrl)
                    .orElse(null);
            if (StrUtil.isBlank(serverUrl)) {
                log.warn("[{}] 未配置 devops.file-sync.server-url，无法上传", TASK_NAME);
                return;
            }
            String uploadUrl = serverUrl + "/devops/server/upload";

            String targetDirOnServer = cfg.getTargetDirOnServer();
            if (StrUtil.isBlank(targetDirOnServer)) {
                // 默认映射为 /{releaseDirName}
                String dirName = releasePath.getFileName() != null ? releasePath.getFileName().toString() : "release";
                targetDirOnServer = "/" + dirName;
            }
            boolean keepZip = Optional.ofNullable(cfg.getKeepZip()).orElse(Boolean.FALSE);

            // 4) 读取历史 SHA1 记录
            Path recordFile = releasePath.resolve(SHA1_RECORD_FILE);
            Map<String, String> sha1Map = loadSha1Map(recordFile);

            // 5) 扫描并比对
            File[] files = releasePath.toFile().listFiles(File::isFile);
            if (files == null || files.length == 0) {
                log.info("[{}] 目录无文件可处理: {}", TASK_NAME, releasePath);
                return;
            }

            int uploaded = 0;
            int skipped = 0;
            for (File f : files) {
                // 跳过记录文件自身
                if (SHA1_RECORD_FILE.equalsIgnoreCase(f.getName())) {
                    continue;
                }
                String nameLower = f.getName().toLowerCase();
                if (!includeExts.isEmpty() && includeExts.stream().noneMatch(nameLower::endsWith)) {
                    continue;
                }

                String newSha1 = FileHashUtil.calculateSHA1(f);
                if (StrUtil.isBlank(newSha1)) {
                    log.warn("[{}] 计算SHA1失败，跳过: {}", TASK_NAME, f.getAbsolutePath());
                    continue;
                }

                String oldSha1 = sha1Map.get(f.getName());
                if (StrUtil.isNotBlank(oldSha1) && oldSha1.equalsIgnoreCase(newSha1)) {
                    skipped++;
                    continue;
                }

                // 6) 上传
                boolean ok = uploadFile(uploadUrl, f, targetDirOnServer, keepZip,
                        devOpsConfig.getFileSync().getDownloadTimeout());
                if (ok) {
                    sha1Map.put(f.getName(), newSha1);
                    uploaded++;
                    log.info("[{}] 上传成功: {} -> {}", TASK_NAME, f.getName(), targetDirOnServer);
                } else {
                    log.error("[{}] 上传失败: {}", TASK_NAME, f.getAbsolutePath());
                }
            }

            // 7) 持久化记录
            if (uploaded > 0) {
                saveSha1Map(recordFile, sha1Map);
            }

            log.info("[{}] 任务完成：上传成功={}，跳过={}，耗时={}ms", TASK_NAME, uploaded, skipped,
                    (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("[{}] 任务执行异常", TASK_NAME, e);
        } finally {
            taskLockManager.releaseLock(TASK_NAME);
        }
    }

    private boolean isWindows() {
        String os = System.getProperty("os.name", "").toLowerCase();
        return os.contains("win");
    }

    private String normalizeWinPath(String p) {
        if (p == null) return null;
        String s = p.replace('\\', '/');
        if (s.matches("^[A-Za-z]:$")) {
            s = s + "/";
        }
        return s;
    }

    private Map<String, String> loadSha1Map(Path recordFile) {
        try {
            if (Files.exists(recordFile)) {
                String json = FileUtil.readString(recordFile.toFile(), StandardCharsets.UTF_8);
                if (StrUtil.isNotBlank(json)) {
                    Map<?, ?> map = JSONUtil.parseObj(json);
                    Map<String, String> res = new HashMap<>();
                    for (Map.Entry<?, ?> e : map.entrySet()) {
                        if (e.getKey() != null && e.getValue() != null) {
                            res.put(String.valueOf(e.getKey()), String.valueOf(e.getValue()));
                        }
                    }
                    return res;
                }
            }
        } catch (Exception e) {
            log.warn("[{}] 读取SHA1记录文件失败: {}", TASK_NAME, recordFile, e);
        }
        return new HashMap<>();
    }

    private void saveSha1Map(Path recordFile, Map<String, String> map) {
        try {
            String json = JSONUtil.toJsonStr(map);
            FileUtil.writeString(json, recordFile.toFile(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn("[{}] 写入SHA1记录文件失败: {}", TASK_NAME, recordFile, e);
        }
    }

    private boolean uploadFile(String uploadUrl, File file, String targetDirOnServer, boolean keepZip, Integer timeoutSeconds) {
        try {
            int timeoutMs = Optional.ofNullable(timeoutSeconds).orElse(300) * 1000;
            HttpRequest request = HttpRequest.post(uploadUrl)
                    .timeout(timeoutMs)
                    .form("targetDir", targetDirOnServer)
                    .form("keepZip", String.valueOf(keepZip))
                    .form("file", file); // multipart/form-data

            HttpResponse resp = request.execute();
            String body = resp.body();
            if (StrUtil.isBlank(body)) {
                log.error("[{}] 上传响应为空: {}", TASK_NAME, file.getName());
                return false;
            }
            CommonResult<?> result = JSONUtil.toBean(body, CommonResult.class);
            if (result != null && Objects.equals(result.getCode(), 0)) {
                return true;
            }
            log.error("[{}] 上传失败，响应: {}", TASK_NAME, body);
            return false;
        } catch (Exception e) {
            log.error("[{}] 上传请求异常: {}", TASK_NAME, e.getMessage(), e);
            return false;
        }
    }
}

