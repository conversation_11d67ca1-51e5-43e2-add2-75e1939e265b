package com.rs.module.devops.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 定时任务锁管理器
 * 用于防止定时任务重复执行，提供快速失败机制
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaskLockManager {

    /**
     * 任务锁映射表
     */
    private final ConcurrentHashMap<String, TaskLock> taskLocks = new ConcurrentHashMap<>();

    /**
     * 任务执行统计
     */
    private final ConcurrentHashMap<String, TaskStatistics> taskStatistics = new ConcurrentHashMap<>();

    /**
     * 尝试获取任务锁
     *
     * @param taskName 任务名称
     * @return 是否成功获取锁
     */
    public boolean tryLock(String taskName) {
        TaskLock taskLock = taskLocks.computeIfAbsent(taskName, k -> new TaskLock());
        TaskStatistics statistics = taskStatistics.computeIfAbsent(taskName, k -> new TaskStatistics());

        boolean acquired = taskLock.running.compareAndSet(false, true);
        
        if (acquired) {
            taskLock.startTime = System.currentTimeMillis();
            taskLock.taskId = generateTaskId(taskName);
            statistics.totalAttempts.incrementAndGet();
            statistics.successfulLocks.incrementAndGet();
            statistics.lastSuccessTime = new Date();
            
            log.info("[{}] 任务锁获取成功，任务开始执行", taskLock.taskId);
            return true;
        } else {
            statistics.totalAttempts.incrementAndGet();
            statistics.failedLocks.incrementAndGet();
            statistics.lastFailTime = new Date();
            
            long runningTime = System.currentTimeMillis() - taskLock.startTime;
            log.warn("[{}] 任务锁获取失败，前一个任务正在执行中，已运行时间: {}ms", 
                    taskName, runningTime);
            return false;
        }
    }

    /**
     * 释放任务锁
     *
     * @param taskName 任务名称
     */
    public void releaseLock(String taskName) {
        TaskLock taskLock = taskLocks.get(taskName);
        if (taskLock != null) {
            long executionTime = System.currentTimeMillis() - taskLock.startTime;
            taskLock.running.set(false);
            
            TaskStatistics statistics = taskStatistics.get(taskName);
            if (statistics != null) {
                statistics.totalExecutionTime.addAndGet(executionTime);
                statistics.completedTasks.incrementAndGet();
                statistics.lastCompletionTime = new Date();
                
                // 更新最长和最短执行时间
                statistics.updateExecutionTime(executionTime);
            }
            
            log.info("[{}] 任务锁释放成功，执行时间: {}ms", taskLock.taskId, executionTime);
        }
    }

    /**
     * 检查任务是否正在运行
     *
     * @param taskName 任务名称
     * @return 是否正在运行
     */
    public boolean isRunning(String taskName) {
        TaskLock taskLock = taskLocks.get(taskName);
        return taskLock != null && taskLock.running.get();
    }

    /**
     * 获取任务运行时间
     *
     * @param taskName 任务名称
     * @return 运行时间（毫秒），如果任务未运行返回-1
     */
    public long getRunningTime(String taskName) {
        TaskLock taskLock = taskLocks.get(taskName);
        if (taskLock != null && taskLock.running.get()) {
            return System.currentTimeMillis() - taskLock.startTime;
        }
        return -1;
    }

    /**
     * 获取任务统计信息
     *
     * @param taskName 任务名称
     * @return 任务统计信息
     */
    public TaskStatistics getTaskStatistics(String taskName) {
        return taskStatistics.get(taskName);
    }

    /**
     * 获取所有任务统计信息
     *
     * @return 所有任务统计信息
     */
    public ConcurrentHashMap<String, TaskStatistics> getAllTaskStatistics() {
        return new ConcurrentHashMap<>(taskStatistics);
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(String taskName) {
        return taskName + "_" + System.currentTimeMillis();
    }

    /**
     * 任务锁内部类
     */
    private static class TaskLock {
        private final AtomicBoolean running = new AtomicBoolean(false);
        private volatile long startTime;
        private volatile String taskId;
    }

    /**
     * 任务统计信息
     */
    public static class TaskStatistics {
        private final AtomicLong totalAttempts = new AtomicLong(0);
        private final AtomicLong successfulLocks = new AtomicLong(0);
        private final AtomicLong failedLocks = new AtomicLong(0);
        private final AtomicLong completedTasks = new AtomicLong(0);
        private final AtomicLong totalExecutionTime = new AtomicLong(0);
        private volatile long maxExecutionTime = 0;
        private volatile long minExecutionTime = Long.MAX_VALUE;
        private volatile Date lastSuccessTime;
        private volatile Date lastFailTime;
        private volatile Date lastCompletionTime;

        public void updateExecutionTime(long executionTime) {
            if (executionTime > maxExecutionTime) {
                maxExecutionTime = executionTime;
            }
            if (executionTime < minExecutionTime) {
                minExecutionTime = executionTime;
            }
        }

        // Getters
        public long getTotalAttempts() { return totalAttempts.get(); }
        public long getSuccessfulLocks() { return successfulLocks.get(); }
        public long getFailedLocks() { return failedLocks.get(); }
        public long getCompletedTasks() { return completedTasks.get(); }
        public long getTotalExecutionTime() { return totalExecutionTime.get(); }
        public long getMaxExecutionTime() { return maxExecutionTime; }
        public long getMinExecutionTime() { return minExecutionTime == Long.MAX_VALUE ? 0 : minExecutionTime; }
        public double getAverageExecutionTime() { 
            long completed = completedTasks.get();
            return completed > 0 ? (double) totalExecutionTime.get() / completed : 0;
        }
        public Date getLastSuccessTime() { return lastSuccessTime; }
        public Date getLastFailTime() { return lastFailTime; }
        public Date getLastCompletionTime() { return lastCompletionTime; }
        
        public double getSuccessRate() {
            long total = totalAttempts.get();
            return total > 0 ? (double) successfulLocks.get() / total * 100 : 0;
        }

        @Override
        public String toString() {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return String.format(
                "TaskStatistics{总尝试次数=%d, 成功获取锁=%d, 获取锁失败=%d, 完成任务=%d, " +
                "总执行时间=%dms, 平均执行时间=%.2fms, 最长执行时间=%dms, 最短执行时间=%dms, " +
                "成功率=%.2f%%, 最后成功时间=%s, 最后失败时间=%s, 最后完成时间=%s}",
                totalAttempts.get(), successfulLocks.get(), failedLocks.get(), completedTasks.get(),
                totalExecutionTime.get(), getAverageExecutionTime(), maxExecutionTime, getMinExecutionTime(),
                getSuccessRate(),
                lastSuccessTime != null ? formatter.format(lastSuccessTime) : "无",
                lastFailTime != null ? formatter.format(lastFailTime) : "无",
                lastCompletionTime != null ? formatter.format(lastCompletionTime) : "无"
            );
        }
    }
}
