package com.rs.module.devops.job;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.util.FileHashUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 光驱扫描复制任务
 *
 * 监听配置的光驱盘符，查找当前日期相关的目录或文件，复制到 C:\\release（可配置）
 */
@Component
@Slf4j
public class CdromScanCopyJob {

    private static final String TASK_NAME = "CDROM_SCAN_COPY";

    @Resource
    private TaskLockManager taskLockManager;

    @Resource
    private DevOpsConfig devOpsConfig;

    @Resource
    ReleaseDirUploadJob releaseDirUploadJob;

    @Scheduled(cron = "#{@devOpsConfig.scheduledTasks.cdromScanCopyCron}")
    public void cdromScanCopyJob() {
        if (!taskLockManager.tryLock(TASK_NAME)) {
            return;
        }
        log.info("光驱扫描复制任务开始执行");

        long start = System.currentTimeMillis();
        try {
            if (!isWindows()) {
                log.info("[{}] 非 Windows 环境，跳过任务", TASK_NAME);
                return;
            }
            DevOpsConfig.CdromCopy cfg = devOpsConfig.getCdromCopy();
            if (cfg == null) {
                log.warn("[{}] 未找到 devops.cdrom-copy 配置，跳过", TASK_NAME);
                return;
            }

            List<String> driveLetters = Optional.ofNullable(cfg.getDriveLetters())
                    .filter(list -> !list.isEmpty())
                    .orElseGet(this::guessCdromLetters);
            if (driveLetters.isEmpty()) {
                log.warn("[{}] 未发现有效光驱盘符，跳过", TASK_NAME);
                return;
            }

            String targetDir = Optional.ofNullable(cfg.getTargetReleaseDir()).orElse("C:/release");
            Path targetPath = Paths.get(normalizeWinPath(targetDir));
            try {
                Files.createDirectories(targetPath);
            } catch (Exception e) {
                log.error("[{}] 创建目标目录失败: {}", TASK_NAME, targetPath, e);
                return;
            }

            List<String> patterns = Optional.ofNullable(cfg.getDatePatterns())
                    .filter(list -> !list.isEmpty())
                    .orElse(Arrays.asList("yyyyMMdd", "yyyy-MM-dd"));
            List<String> todayNames = buildTodayNames(patterns);
            Set<String> includeExts = Optional.ofNullable(cfg.getIncludeExtensions())
                    .map(list -> list.stream().filter(StrUtil::isNotBlank)
                            .map(String::toLowerCase).collect(Collectors.toSet()))
                    .orElse(Collections.emptySet());
            boolean onlyToday = Optional.ofNullable(cfg.getOnlyToday()).orElse(Boolean.TRUE);
            boolean overwrite = Optional.ofNullable(cfg.getOverwrite()).orElse(Boolean.TRUE);

            int copied = 0;
            log.info("[{}] 扫描盘符: {}，目标目录: {}，仅当天: {}，后缀过滤: {}，覆盖: {}",
                    TASK_NAME, driveLetters, targetPath, onlyToday, includeExts, overwrite);
            for (String letter : driveLetters) {
                String root = normalizeWinPath(letter);
                if (!root.endsWith("/")) root = root + "/"; // 统一以 / 结尾
                Path driveRoot = Paths.get(root);
                if (!Files.exists(driveRoot) || !Files.isDirectory(driveRoot)) {
                    log.debug("[{}] 盘符不存在或不可用: {}", TASK_NAME, driveRoot);
                    continue;
                }
                    int copiedBeforeDrive = copied;

                try {
                    // 1) 优先复制根下与当天同名的目录
                    for (String dn : todayNames) {
                        Path dateDir = driveRoot.resolve(dn);
                        if (Files.isDirectory(dateDir)) {
                            Path targetDateDir = targetPath.resolve(dn);
                            log.info("[{}] 发现当天目录: {}，开始复制到: {}", TASK_NAME, dateDir, targetDateDir);
                            copied += copyDirectoryFiltered(dateDir, targetDateDir, includeExts, overwrite);
                            log.info("[{}] 目录复制完成: {} -> {}", TASK_NAME, dateDir, targetDateDir);
                        }
                    }
                    // 2) 根目录下当日名称前缀的文件（如 20250808_xxx.pdf）
                    File[] rootFiles = driveRoot.toFile().listFiles();
                    if (rootFiles != null) {
                        for (File f : rootFiles) {
                            if (!f.isFile()) continue;
                            String lower = f.getName().toLowerCase();
                            boolean matchToday = todayNames.stream().anyMatch(lower::startsWith);
                            if ((onlyToday && !matchToday)) {
                                log.debug("[{}] 非当天前缀，跳过: {}", TASK_NAME, f.getName());
                                continue;
                            }
                            if (!includeExts.isEmpty() && includeExts.stream().noneMatch(lower::endsWith)) {
                                log.debug("[{}] 不在扩展名白名单，跳过: {}", TASK_NAME, f.getName());
                                continue;
                            }
                            Path dest = targetPath.resolve(f.getName());
                            try {
                                File destFile = dest.toFile();
                                if (destFile.exists() && destFile.isFile()) {
                                    String srcSha1 = FileHashUtil.calculateSHA1(f);
                                    String dstSha1 = FileHashUtil.calculateSHA1(destFile);
                                    log.debug("[{}] 比较SHA1 源:{} 目:{}", TASK_NAME, srcSha1, dstSha1);
                                    if (srcSha1 != null && srcSha1.equalsIgnoreCase(dstSha1)) {
                                        log.info("[{}] SHA1一致，跳过复制: {} -> {}", TASK_NAME, f.getName(), destFile.getAbsolutePath());
                                        continue;
                                    }
                                }
                                log.info("[{}] 复制文件: {} -> {} (overwrite={})", TASK_NAME, f.getAbsolutePath(), destFile.getAbsolutePath(), overwrite);
                                FileUtil.copy(f, destFile, overwrite);
                                copied++;
                            } catch (Exception e) {
                                log.warn("复制文件失败: {} -> {}", f, dest, e);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("[{}] 扫描盘符失败: {}", TASK_NAME, driveRoot, e);
                } finally {
                    try {
                        try {
                            releaseDirUploadJob.releaseUploadJob();
                        } catch (Exception e) {
                            log.error("[{}] 上传任务执行异常", TASK_NAME, e);
                        }
                        // 仅当本盘符复制了至少一个文件才尝试弹出/弹窗
                        if (copied > copiedBeforeDrive) {

                            //上传文件
                            boolean ok = ejectDrive(letter);
                            log.info("[{}] 弹出光驱{}：{}", TASK_NAME, letter, ok ? "成功" : "失败");
                            if (!ok) {
                                notifyCopyCompleted(letter, targetPath);
                            }
                        } else {
                            log.info("[{}] 盘符{}未复制任何文件，不执行弹出/弹窗", TASK_NAME, letter);
                        }
                    } catch (Exception ex) {
                        log.warn("[{}] 弹出光驱{}异常", TASK_NAME, letter, ex);
                    }
                }
            }
            log.info("[{}] 任务完成：复制文件数={}，耗时={}ms", TASK_NAME, copied, (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("[{}] 任务执行异常", TASK_NAME, e);
        } finally {
            taskLockManager.releaseLock(TASK_NAME);
        }
    }

    private boolean isWindows() {
        String os = System.getProperty("os.name", "").toLowerCase();
        return os.contains("win");
    }

    private List<String> buildTodayNames(List<String> patterns) {
        LocalDate today = LocalDate.now();
        List<String> list = new ArrayList<>();
        for (String p : patterns) {
            try {
                list.add(today.format(DateTimeFormatter.ofPattern(p)));
            } catch (Exception ignore) {
            }
        }
        return list;
    }

    private String normalizeWinPath(String p) {
        if (p == null) return null;
        // 统一使用 /，避免 \\ 的转义问题
        String s = p.replace('\\', '/');
        // 补全如 "D:" -> "D:/"
        if (s.matches("^[A-Za-z]:$")) {
            s = s + "/";
        }
        return s;
    }

    private List<String> guessCdromLetters() {
        // 简单猜测 D:~Z: 中可读的只读盘符
        List<String> guess = new ArrayList<>();
        for (char c = 'D'; c <= 'Z'; c++) {
            String root = (c + ":/");
            try {
                File f = new File(root);
                if (f.exists() && f.isDirectory() && f.canRead()) {
                    // 不包含系统盘 C:，其余的认为可能是移动介质/光驱
                    guess.add(c + ":");
                }
            } catch (Exception ignore) {
            }
        }
        return guess;
    }

    private int copyDirectoryFiltered(Path srcDir, Path destDir, Set<String> includeExts, boolean overwrite) {
        if (srcDir == null || !Files.isDirectory(srcDir)) return 0;
        int count = 0;
        try {
            Files.createDirectories(destDir);
        } catch (Exception e) {
            log.warn("[{}] 创建目标目录失败: {}", TASK_NAME, destDir, e);
            return 0;
        }
        File[] files = srcDir.toFile().listFiles();
        if (files == null) return 0;
        for (File f : files) {
            Path dest = destDir.resolve(f.getName());
            if (f.isDirectory()) {
                count += copyDirectoryFiltered(f.toPath(), dest, includeExts, overwrite);
            } else {
                String lower = f.getName().toLowerCase();
                if (!includeExts.isEmpty() && includeExts.stream().noneMatch(lower::endsWith)) continue;
                try {
                    File destFile = dest.toFile();
                    if (destFile.exists() && destFile.isFile()) {
                        String srcSha1 = FileHashUtil.calculateSHA1(f);
                        String dstSha1 = FileHashUtil.calculateSHA1(destFile);
                        log.debug("[{}] 比较SHA1 源:{} 目:{}", TASK_NAME, srcSha1, dstSha1);
                        if (srcSha1 != null && srcSha1.equalsIgnoreCase(dstSha1)) {
                            log.info("[{}] SHA1一致，跳过复制: {} -> {}", TASK_NAME, f.getAbsolutePath(), destFile.getAbsolutePath());
                            continue;
                        }
                    }
                    log.info("[{}] 复制文件: {} -> {} (overwrite={})", TASK_NAME, f.getAbsolutePath(), destFile.getAbsolutePath(), overwrite);
                    FileUtil.copy(f, destFile, overwrite);
                    count++;
                } catch (Exception e) {
                    log.warn("[{}] 复制文件失败: {} -> {}", TASK_NAME, f, dest, e);
                }
            }
        }
        return count;
    }

    /**
     * 弹出光驱托盘（Windows）
     */
    private void notifyCopyCompleted(String driveLetter, Path targetDir) {
        try {
            if (GraphicsEnvironment.isHeadless()) {
                log.warn("[{}] 当前为无头环境，无法弹窗。复制已完成，请手动弹出光驱{}。", TASK_NAME, driveLetter);
                return;
            }
            SwingUtilities.invokeLater(() -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    String msg = String.format("从光驱 %s 复制完成：%s\n请手动弹出光驱。", driveLetter, targetDir);
                    JOptionPane.showMessageDialog(null, msg, "复制完成", JOptionPane.INFORMATION_MESSAGE);
                } catch (Exception ex) {
                    log.warn("[{}] 显示复制完成弹窗失败", TASK_NAME, ex);
                }
            });
        } catch (Exception e) {
            log.warn("[{}] 通知复制完成失败", TASK_NAME, e);
        }
    }

    private boolean ejectDrive(String driveLetter) {
        try {
            if (driveLetter == null || driveLetter.isEmpty()) return false;
            String dl = driveLetter.replace("/", "").replace("\\", "");
            if (!dl.endsWith(":")) dl = dl + ":";
            String psCmd = "$sh = New-Object -ComObject Shell.Application; $drv=$sh.NameSpace(17).ParseName('" + dl + "'); if ($drv) { $drv.InvokeVerb('Eject') }";
            ProcessBuilder pb = new ProcessBuilder("powershell", "-NoProfile", "-NonInteractive", "-Command", psCmd);
            Process p = pb.start();
            boolean finished = p.waitFor(10, java.util.concurrent.TimeUnit.SECONDS);
            int code = finished ? p.exitValue() : -1;
            log.debug("[{}] 执行弹出命令: {}，完成:{}，exitCode:{}", TASK_NAME, psCmd, finished, code);
            return finished && code == 0;
        } catch (Exception e) {
            log.warn("[{}] 调用弹出光驱命令失败: {}", TASK_NAME, driveLetter, e);
            return false;
        }
    }
}

