package com.rs.module.devops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 客户端报告VO（包含对比报告和重启日志）
 *
 * <AUTHOR>
 */
@ApiModel(description = "客户端报告VO")
@Data
public class ClientReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "对比报告")
    private CompareReportVO compareReport;

    @ApiModelProperty(value = "重启日志列表")
    private List<RestartLogVO> restartLogs;

    @ApiModelProperty(value = "同步状态")
    private String syncStatus;

    @ApiModelProperty(value = "最后同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSyncTime;

    @ApiModelProperty(value = "同步结果")
    private Boolean syncResult;

    @ApiModelProperty(value = "同步进度详情")
    private SyncProgressVO syncProgress;

    @ApiModelProperty(value = "是否暂停比较")
    private Boolean comparePaused = false;

    @ApiModelProperty(value = "暂停时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pauseTime;

    @ApiModelProperty(value = "暂停原因")
    private String pauseReason;

    /**
     * 重启日志VO
     */
    @ApiModel(description = "重启日志VO")
    @Data
    public static class RestartLogVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "重启时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date restartTime;

        @ApiModelProperty(value = "应用名称")
        private String appName;

        @ApiModelProperty(value = "重启结果")
        private Boolean success;

        @ApiModelProperty(value = "错误信息")
        private String errorMessage;

        @ApiModelProperty(value = "执行耗时（毫秒）")
        private Long executionTime;

        @ApiModelProperty(value = "重启类型")
        private String restartType;

        @ApiModelProperty(value = "相关文件")
        private List<String> relatedFiles;
    }

    public ClientReportVO() {
        this.lastUpdateTime = new Date();
        this.syncStatus = "PENDING";
    }

    public ClientReportVO(String clientIp) {
        this();
        this.clientIp = clientIp;
    }

    /**
     * 更新对比报告
     */
    public void updateCompareReport(CompareReportVO compareReport) {
        this.compareReport = compareReport;
        this.lastUpdateTime = new Date();
        this.syncStatus = "COMPARED";
    }

    /**
     * 更新同步状态
     */
    public void updateSyncStatus(Boolean syncResult) {
        this.syncResult = syncResult;
        this.lastSyncTime = new Date();
        this.syncStatus = syncResult ? "SYNC_SUCCESS" : "SYNC_FAILED";
        this.lastUpdateTime = new Date();
    }

    /**
     * 更新同步进度
     */
    public void updateSyncProgress(SyncProgressVO syncProgress) {
        this.syncProgress = syncProgress;
        this.lastUpdateTime = new Date();

        // 根据进度更新同步状态
        if (syncProgress != null) {
            if (syncProgress.isCompleted()) {
                this.syncResult = syncProgress.isSuccess();
                this.lastSyncTime = syncProgress.getSyncEndTime();
                this.syncStatus = syncProgress.isSuccess() ? "SYNC_SUCCESS" : "SYNC_FAILED";
            } else {
                this.syncStatus = "SYNCING";
            }
        }
    }

    /**
     * 添加重启日志
     */
    public void addRestartLog(RestartLogVO restartLog) {
        if (this.restartLogs == null) {
            this.restartLogs = new java.util.ArrayList<>();
        }
        this.restartLogs.add(restartLog);
        this.lastUpdateTime = new Date();
    }

    /**
     * 暂停比较
     */
    public void pauseCompare(String reason) {
        this.comparePaused = true;
        this.pauseTime = new Date();
        this.pauseReason = reason;
        this.lastUpdateTime = new Date();
    }

    /**
     * 恢复比较
     */
    public void resumeCompare() {
        this.comparePaused = false;
        this.pauseTime = null;
        this.pauseReason = null;
        this.lastUpdateTime = new Date();
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (Boolean.TRUE.equals(comparePaused)) {
            return "比较已暂停";
        }

        switch (syncStatus) {
            case "PENDING":
                return "等待对比";
            case "COMPARED":
                return "已对比";
            case "SYNCING":
                return "同步中";
            case "SYNC_SUCCESS":
                return "同步成功";
            case "SYNC_FAILED":
                return "同步失败";
            default:
                return "未知状态";
        }
    }
}
