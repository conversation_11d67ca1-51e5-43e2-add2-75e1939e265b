package com.rs.module.devops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 文件对比报告VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "文件对比报告VO")
@Data
public class CompareReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "对比时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date compareTime;

    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    @ApiModelProperty(value = "客户端扫描文件总数")
    private Integer clientFileCount;

    @ApiModelProperty(value = "服务端扫描文件总数")
    private Integer serverFileCount;

    @ApiModelProperty(value = "需要新增的文件列表")
    private List<FileInfoVO> filesToAdd;

    @ApiModelProperty(value = "需要更新的文件列表")
    private List<FileInfoVO> filesToUpdate;

    @ApiModelProperty(value = "需要删除的文件列表")
    private List<FileInfoVO> filesToDelete;

    @ApiModelProperty(value = "对比摘要信息")
    private String summary;

    @ApiModelProperty(value = "是否有差异")
    private Boolean hasDifferences;

    public CompareReportVO() {
        this.compareTime = new Date();
        this.hasDifferences = false;
    }

    /**
     * 计算是否有差异
     */
    public void calculateDifferences() {
        this.hasDifferences = (filesToAdd != null && !filesToAdd.isEmpty()) ||
                             (filesToUpdate != null && !filesToUpdate.isEmpty()) ||
                             (filesToDelete != null && !filesToDelete.isEmpty());
    }

    /**
     * 生成摘要信息
     */
    public void generateSummary() {
        int addCount = filesToAdd != null ? filesToAdd.size() : 0;
        int updateCount = filesToUpdate != null ? filesToUpdate.size() : 0;
        int deleteCount = filesToDelete != null ? filesToDelete.size() : 0;
        
        this.summary = String.format("对比完成：新增 %d 个文件，更新 %d 个文件，删除 %d 个文件", 
                                    addCount, updateCount, deleteCount);
    }
}
