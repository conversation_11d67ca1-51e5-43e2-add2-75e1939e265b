package com.rs.module.devops.service;

import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * DevOps服务端服务接口
 *
 * <AUTHOR>
 */
public interface DevOpsServerService {

    /**
     * 扫描服务端文件
     *
     * @return 文件信息列表
     */
    List<FileInfoVO> scanServerFiles();

    /**
     * 对比客户端和服务端文件
     *
     * @param clientFiles 客户端文件列表
     * @param clientIp 客户端IP
     * @return 对比报告
     */
    CompareReportVO compareFiles(List<FileInfoVO> clientFiles, String clientIp);

    /**
     * 获取文件内容
     *
     * @param filePath 文件路径
     * @return 文件字节数组
     */
    byte[] getFileContent(String filePath);

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    Boolean fileExists(String filePath);

    /**
     * 获取文件信息
     *
     * @param filePath 文件路径
     * @return 文件信息
     */
    FileInfoVO getFileInfo(String filePath);

    /**
     * 记录同步日志
     *
     * @param clientIp 客户端IP
     * @param operation 操作类型
     * @param filePath 文件路径
     * @param result 操作结果
     */
    void logSyncOperation(String clientIp, String operation, String filePath, Boolean result);

    /**
     * 获取同步统计信息
     *
     * @param clientIp 客户端IP（可选）
     * @return 统计信息
     */
    Object getSyncStatistics(String clientIp);

    /**
     * 接收上传文件并覆盖到指定目录，若文件为zip则在目标目录解压
     * @param file 上传的文件
     * @param targetDir 目标目录的绝对路径
     * @param keepZip 是否保留上传后的zip源文件，默认false删除
     * @return 结果信息，包含上传路径、是否解压等
     */
    Map<String, Object> uploadFileToDir(MultipartFile file, String targetDir, boolean keepZip);
}
