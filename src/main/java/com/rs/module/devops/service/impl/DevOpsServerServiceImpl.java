package com.rs.module.devops.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.service.ClientReportService;
import com.rs.module.devops.service.DevOpsServerService;
import com.rs.module.devops.util.FileHashUtil;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * DevOps服务端服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DevOpsServerServiceImpl implements DevOpsServerService {

    @Autowired
    private DevOpsConfig devOpsConfig;

    @Autowired
    private ClientReportService clientReportService;

    @Override
    public List<FileInfoVO> scanServerFiles() {
        log.info("开始扫描服务端文件...");
        List<FileInfoVO> fileInfoList = new ArrayList<>();

        List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
        if (scanDirectories == null || scanDirectories.isEmpty()) {
            log.warn("未配置扫描目录");
            return fileInfoList;
        }

        for (String directory : scanDirectories) {
            try {
                scanDirectory(directory, fileInfoList);
            } catch (Exception e) {
                log.error("扫描目录 {} 时发生错误: {}", directory, e.getMessage(), e);
            }
        }

        log.info("服务端文件扫描完成，共扫描到 {} 个文件", fileInfoList.size());
        return fileInfoList;
    }

    private void scanDirectory(String directoryPath, List<FileInfoVO> fileInfoList) {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path) || !Files.isDirectory(path)) {
            log.warn("目录不存在或不是有效目录: {}", directoryPath);
            return;
        }

        try (Stream<Path> paths = Files.walk(path)) {
            paths.filter(Files::isRegularFile)
                    .filter(this::shouldIncludeFile)
                    .forEach(filePath -> {
                        try {
                            FileInfoVO fileInfo = createFileInfo(filePath);
                            if (fileInfo != null) {
                                fileInfoList.add(fileInfo);
                            }
                        } catch (Exception e) {
                            log.error("处理文件 {} 时发生错误: {}", filePath, e.getMessage());
                        }
                    });
        } catch (IOException e) {
            log.error("遍历目录 {} 时发生错误: {}", directoryPath, e.getMessage(), e);
        }
    }

    private boolean shouldIncludeFile(Path filePath) {
        String fileName = filePath.getFileName().toString();

        // 检查文件扩展名
        List<String> fileExtensions = devOpsConfig.getJarScan().getFileExtensions();
        if (fileExtensions != null && !fileExtensions.isEmpty()) {
            boolean hasValidExtension = fileExtensions.stream()
                    .anyMatch(ext -> fileName.toLowerCase().endsWith(ext.toLowerCase()));
            if (!hasValidExtension) {
                return false;
            }
        }

        // 检查排除目录
        List<String> excludeDirectories = devOpsConfig.getJarScan().getExcludeDirectories();
        if (excludeDirectories != null) {
            String pathStr = filePath.toString();
            for (String excludeDir : excludeDirectories) {
                if (pathStr.contains(excludeDir)) {
                    return false;
                }
            }
        }

        // 检查文件大小
        Long maxFileSize = devOpsConfig.getJarScan().getMaxFileSize();
        if (maxFileSize != null && maxFileSize > 0) {
            try {
                long fileSize = Files.size(filePath);
                if (fileSize > maxFileSize) {
                    return false;
                }
            } catch (IOException e) {
                log.warn("无法获取文件大小: {}", filePath);
                return false;
            }
        }

        return true;
    }

    private FileInfoVO createFileInfo(Path filePath) {
        try {
            File file = filePath.toFile();
            FileInfoVO fileInfo = new FileInfoVO();

            fileInfo.setFilePath(file.getAbsolutePath());
            fileInfo.setFileName(file.getName());
            fileInfo.setFileSize(file.length());
            fileInfo.setLastModified(new Date(file.lastModified()));
            fileInfo.setIsDirectory(file.isDirectory());

            // 设置相对路径
            String relativePath = getRelativePath(file.getAbsolutePath());
            fileInfo.setRelativePath(relativePath);

            // 设置文件扩展名
            String extension = FileUtil.extName(file.getName());
            fileInfo.setFileExtension(StrUtil.isNotBlank(extension) ? "." + extension : "");

            // 计算哈希值
            if (devOpsConfig.getJarScan().getCalculateMd5()) {
                fileInfo.setMd5Hash(FileHashUtil.calculateMD5(file));
            }
            if (devOpsConfig.getJarScan().getCalculateSha1()) {
                fileInfo.setSha1Hash(FileHashUtil.calculateSHA1(file));
            }

            return fileInfo;
        } catch (Exception e) {
            log.error("创建文件信息时发生错误: {}", filePath, e);
            return null;
        }
    }

    private String getRelativePath(String absolutePath) {
        List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
        for (String baseDir : scanDirectories) {
            try {
                Path basePath = Paths.get(baseDir).toAbsolutePath().normalize();
                Path filePath = Paths.get(absolutePath).toAbsolutePath().normalize();
                if (filePath.startsWith(basePath)) {
                    return basePath.relativize(filePath).toString();
                }
            } catch (Exception e) {
                log.debug("计算相对路径时发生错误: {}", e.getMessage());
            }
        }
        return absolutePath;
    }

    @Override
    public CompareReportVO compareFiles(List<FileInfoVO> clientFiles, String clientIp) {
        log.info("开始对比客户端 {} 的文件，客户端文件数量: {}", clientIp, clientFiles.size());

        CompareReportVO compareReport = new CompareReportVO();
        compareReport.setClientIp(clientIp);
        compareReport.setClientFileCount(clientFiles.size());

        // 扫描服务端文件
        log.info("开始扫描服务端文件 - 客户端: {}", clientIp);
        List<FileInfoVO> serverFiles = scanServerFiles();
        compareReport.setServerFileCount(serverFiles.size());
        log.info("服务端文件扫描完成 - 客户端: {}, 服务端文件数量: {}", clientIp, serverFiles.size());

        // 创建服务端文件映射（以相对路径为key）
        log.debug("创建服务端文件映射 - 客户端: {}", clientIp);
        Map<String, FileInfoVO> serverFileMap = serverFiles.stream()
                .collect(Collectors.toMap(FileInfoVO::getFilePath, f -> f, (f1, f2) -> f1));

        // 创建客户端文件映射
        log.debug("创建客户端文件映射 - 客户端: {}", clientIp);
        Map<String, FileInfoVO> clientFileMap = clientFiles.stream()
                .collect(Collectors.toMap(FileInfoVO::getFilePath, f -> f, (f1, f2) -> f1));

        List<FileInfoVO> filesToAdd = new ArrayList<>();
        List<FileInfoVO> filesToUpdate = new ArrayList<>();
        List<FileInfoVO> filesToDelete = new ArrayList<>();

        // 检查服务端文件，找出需要新增或更新的文件
        log.info("开始检查服务端文件，查找需要新增或更新的文件 - 客户端: {}", clientIp);
        for (FileInfoVO serverFile : serverFiles) {
            String relativePath = serverFile.getFilePath();
            FileInfoVO clientFile = clientFileMap.get(relativePath);

            if (clientFile == null) {
                // 客户端没有此文件，需要新增
                log.debug("发现需要新增的文件 - 客户端: {}, 文件: {}", clientIp, relativePath);
                filesToAdd.add(serverFile);
            } else {
                // 比较文件是否需要更新
                if (needsUpdate(serverFile, clientFile)) {
                    log.debug("发现需要更新的文件 - 客户端: {}, 文件: {}", clientIp, relativePath);
                    filesToUpdate.add(serverFile);
                }
            }
        }
        log.info("服务端文件检查完成 - 客户端: {}, 需要新增: {}, 需要更新: {}", clientIp, filesToAdd.size(), filesToUpdate.size());

        // 检查客户端文件，找出需要删除的文件（服务端没有的文件）
        log.info("开始检查客户端文件，查找需要删除的文件 - 客户端: {}", clientIp);
        int obsoleteFileCount = 0;
        for (FileInfoVO clientFile : clientFiles) {
            String relativePath = clientFile.getFilePath();
            if (!serverFileMap.containsKey(relativePath)) {
                obsoleteFileCount++;
                log.debug("发现客户端多余文件（暂不删除）- 客户端: {}, 文件: {}", clientIp, relativePath);
                //暂不删除
//                filesToDelete.add(clientFile);
            }
        }
        log.info("客户端文件检查完成 - 客户端: {}, 发现多余文件: {} 个（暂不删除）", clientIp, obsoleteFileCount);

        compareReport.setFilesToAdd(filesToAdd);
        compareReport.setFilesToUpdate(filesToUpdate);
        compareReport.setFilesToDelete(filesToDelete);

        log.info("开始生成对比报告 - 客户端: {}", clientIp);
        compareReport.calculateDifferences();
        compareReport.generateSummary();

        // 记录对比日志
        logSyncOperation(clientIp, "COMPARE", "ALL_FILES", true);

        // 保存对比报告到内存
        log.debug("保存对比报告到内存 - 客户端: {}", clientIp);
        clientReportService.saveCompareReport(clientIp, compareReport);

        log.info("文件对比完成 - 客户端: {}, {}", clientIp, compareReport.getSummary());
        return compareReport;
    }

    private boolean needsUpdate(FileInfoVO serverFile, FileInfoVO clientFile) {
        String relativePath = serverFile.getRelativePath();

        // 比较文件大小
//        if (!serverFile.getFileSize().equals(clientFile.getFileSize())) {
//            log.debug("文件需要更新（大小不同）- 文件: {}, 服务端大小: {}, 客户端大小: {}",
//                    relativePath, serverFile.getFileSize(), clientFile.getFileSize());
//            return true;
//        }

        // 比较修改时间
//        if (!serverFile.getLastModified().equals(clientFile.getLastModified())) {
//            log.debug("文件需要更新（修改时间不同）- 文件: {}, 服务端时间: {}, 客户端时间: {}",
//                    relativePath, serverFile.getLastModified(), clientFile.getLastModified());
//            return true;
//        }

        // 比较MD5哈希值
//        if (StrUtil.isNotBlank(serverFile.getMd5Hash()) && StrUtil.isNotBlank(clientFile.getMd5Hash())) {
//            boolean md5Different = !serverFile.getMd5Hash().equalsIgnoreCase(clientFile.getMd5Hash());
//            if (md5Different) {
//                log.debug("文件需要更新（MD5不同）- 文件: {}, 服务端MD5: {}, 客户端MD5: {}",
//                        relativePath, serverFile.getMd5Hash(), clientFile.getMd5Hash());
//            }
//            return md5Different;
//        }

        // 比较SHA1哈希值
        if (StrUtil.isNotBlank(serverFile.getSha1Hash()) && StrUtil.isNotBlank(clientFile.getSha1Hash())) {
            boolean sha1Different = !serverFile.getSha1Hash().equalsIgnoreCase(clientFile.getSha1Hash());
            if (sha1Different) {
                log.debug("文件需要更新（SHA1不同）- 文件: {}, 服务端SHA1: {}, 客户端SHA1: {}",
                        relativePath, serverFile.getSha1Hash(), clientFile.getSha1Hash());
            }
            return sha1Different;
        }

        log.debug("文件无需更新 - 文件: {}", relativePath);
        return false;
    }

    @Override
    public byte[] getFileContent(String filePath) {
        try {
            if (!FileUtil.exist(filePath)) {
                log.warn("文件不存在: {}", filePath);
                return null;
            }

            return FileUtil.readBytes(filePath);
        } catch (Exception e) {
            log.error("读取文件内容时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    private String getFullFilePath(String relativePath) {
        List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
        if (scanDirectories != null && !scanDirectories.isEmpty()) {
            return Paths.get(scanDirectories.get(0), relativePath).toString();
        }
        return relativePath;
    }

    @Override
    public Boolean fileExists(String fullPath) {
        return FileUtil.exist(fullPath);
    }

    @Override
    public FileInfoVO getFileInfo(String fullPath) {
        try {
            if (!FileUtil.exist(fullPath)) {
                return null;
            }

            Path path = Paths.get(fullPath);
            return createFileInfo(path);
        } catch (Exception e) {
            log.error("获取文件信息时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void logSyncOperation(String clientIp, String operation, String filePath, Boolean result) {
        String resultStr = result ? "成功" : "失败";
        log.info("同步操作记录 - 客户端: {}, 操作: {}, 文件: {}, 结果: {}, 时间: {}",
                clientIp, operation, filePath, resultStr, new Date());
    }

    @Override
    public Object getSyncStatistics(String clientIp) {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("serverFileCount", scanServerFiles().size());
        statistics.put("lastScanTime", new Date());

        if (StrUtil.isNotBlank(clientIp)) {
            statistics.put("clientIp", clientIp);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> uploadFileToDir(MultipartFile file, String targetDir, boolean keepZip) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (file == null || file.isEmpty()) {
                throw new IllegalArgumentException("上传文件为空");
            }
            if (StrUtil.isBlank(targetDir)) {
                throw new IllegalArgumentException("目标目录不能为空");
            }

            File targetDirectory = new File(targetDir);
            if (!targetDirectory.exists() && !targetDirectory.mkdirs()) {
                throw new IllegalStateException("创建目标目录失败: " + targetDir);
            }

            // 保存上传文件到目标目录
            String originalFilename = StrUtil.blankToDefault(file.getOriginalFilename(), "upload.bin");
            File targetFile = new File(targetDirectory, originalFilename);
            file.transferTo(targetFile);

            result.put("savedPath", targetFile.getAbsolutePath());
            result.put("fileName", originalFilename);
            result.put("fileSize", targetFile.length());

            // 如果是zip，解压到目标目录
            if (originalFilename.toLowerCase().endsWith(".zip")) {
                try {
                    // 兼容中文文件名编码
                    try {
                        java.util.zip.ZipFile zipFile = new java.util.zip.ZipFile(targetFile, Charset.forName("UTF-8"));
                        ZipUtil.unzip(zipFile, targetDirectory);
                    } catch (Exception e) {
                        java.util.zip.ZipFile zipFile = new java.util.zip.ZipFile(targetFile, Charset.forName("GBK"));
                        ZipUtil.unzip(zipFile, targetDirectory);
                    }
                    result.put("unzipped", true);
                } catch (Exception e) {
                    log.error("解压ZIP失败: {}", e.getMessage(), e);
                    result.put("unzipped", false);
                    throw e;
                } finally {
                    if (!keepZip && targetFile.exists()) {
                        FileUtil.del(targetFile);
                        result.put("zipDeleted", true);
                    } else {
                        result.put("zipDeleted", false);
                    }
                }
            } else {
                result.put("unzipped", false);
                result.put("zipDeleted", false);
            }

            return result;
        } catch (Exception e) {
            log.error("上传并处理文件失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return result;
        }
    }
}

