package com.rs.module.devops.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.service.AppRestartService;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import com.rs.module.devops.vo.RestartResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 应用重启服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppRestartServiceImpl implements AppRestartService {

    @Autowired
    private DevOpsConfig devOpsConfig;

    @Override
    public Set<String> determineAppsToRestart(CompareReportVO compareReport) {
        if (!isRestartEnabled()) {
            return Collections.emptySet();
        }

        Set<String> appsToRestart = new HashSet<>();
        Map<String, String> directoryAppMapping = devOpsConfig.getAppRestart().getDirectoryAppMapping();

        if (directoryAppMapping == null || directoryAppMapping.isEmpty()) {
            log.debug("未配置目录与应用的映射关系");
            return appsToRestart;
        }

        // 调试日志：打印配置的映射关系
        log.debug("目录与应用映射关系配置: {}", directoryAppMapping);

        // 收集所有有变化的文件路径
        Set<String> changedFilePaths = new HashSet<>();

        if (compareReport.getFilesToAdd() != null) {
            changedFilePaths.addAll(compareReport.getFilesToAdd().stream()
                    .map(FileInfoVO::getFilePath)
                    .collect(Collectors.toSet()));
        }

        if (compareReport.getFilesToUpdate() != null) {
            changedFilePaths.addAll(compareReport.getFilesToUpdate().stream()
                    .map(FileInfoVO::getFilePath)
                    .collect(Collectors.toSet()));
        }

        if (compareReport.getFilesToDelete() != null) {
            changedFilePaths.addAll(compareReport.getFilesToDelete().stream()
                    .map(FileInfoVO::getFilePath)
                    .collect(Collectors.toSet()));
        }

        // 根据文件路径确定需要重启的应用
        for (String filePath : changedFilePaths) {
            for (Map.Entry<String, String> entry : directoryAppMapping.entrySet()) {
                String configuredDirectory = entry.getValue();
                String appName = entry.getKey();

                if (StrUtil.isBlank(appName)) {
                    continue;
                }

                // 检查文件是否在配置的目录下
                if (isFileInDirectory(filePath, configuredDirectory)) {
                    appsToRestart.add(appName);
                    log.info("文件 {} 在目录 {} 下发生变化，需要重启应用: {}", filePath, configuredDirectory, appName);
                }
            }
        }

        return appsToRestart;
    }

    @Override
    public RestartResultVO restartApps(Set<String> appNames, String clientIp) {
        RestartResultVO result = new RestartResultVO();
        result.setClientIp(clientIp);
        result.setRestartDetails(new ArrayList<>());

        if (appNames == null || appNames.isEmpty()) {
            result.setSummary("无应用需要重启");
            return result;
        }

        log.info("开始重启应用，客户端: {}，应用列表: {}", clientIp, appNames);

        // 重启前延迟
        if (devOpsConfig.getAppRestart().getEnableRestartDelay()) {
            Integer delaySeconds = devOpsConfig.getAppRestart().getRestartDelaySeconds();
            if (delaySeconds != null && delaySeconds > 0) {
                log.info("重启前延迟 {} 秒...", delaySeconds);
                try {
                    Thread.sleep(delaySeconds * 1000L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("重启延迟被中断");
                }
            }
        }

        // 逐个重启应用
        for (String appName : appNames) {
            String directory = getDirectoryByAppName(appName);
            RestartResultVO.AppRestartDetailVO detail = restartSingleApp(appName, directory);
            result.getRestartDetails().add(detail);
        }

        result.generateSummary();

        if (devOpsConfig.getAppRestart().getEnableRestartLog()) {
            log.info("应用重启完成 - 客户端: {}, 结果: {}", clientIp, result.getSummary());
        }

        return result;
    }

    @Override
    public RestartResultVO.AppRestartDetailVO restartSingleApp(String appName, String directory) {
        RestartResultVO.AppRestartDetailVO detail = new RestartResultVO.AppRestartDetailVO();
        detail.setAppName(appName);
        detail.setDirectory(directory);

        try {
            String commandTemplate = devOpsConfig.getAppRestart().getRestartCommandTemplate();
            String command = commandTemplate.replace("{appName}", appName);

            // 如果是Docker模式，添加Docker主机命令前缀
            if (devOpsConfig.getAppRestart().getDockerMode()) {
                String dockerHostCommand = devOpsConfig.getAppRestart().getDockerHostCommand();
                if (StrUtil.isNotBlank(dockerHostCommand)) {
                    command = dockerHostCommand + " " + command;
                }
            }

            detail.setRestartCommand(command);

            return executeRestartCommand(command, appName);

        } catch (Exception e) {
            detail.setSuccess(false);
            detail.setErrorMessage("重启应用时发生错误: " + e.getMessage());
            log.error("重启应用 {} 时发生错误: {}", appName, e.getMessage(), e);
            return detail;
        }
    }

    @Override
    public RestartResultVO.AppRestartDetailVO executeRestartCommand(String command, String appName) {
        RestartResultVO.AppRestartDetailVO detail = new RestartResultVO.AppRestartDetailVO();
        detail.setAppName(appName);
        detail.setRestartCommand(command);

        long startTime = System.currentTimeMillis();

        try {
            log.info("执行重启命令: {}", command);

            ProcessBuilder processBuilder = new ProcessBuilder();

            // 根据操作系统设置命令
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                processBuilder.command("cmd", "/c", command);
            } else {
                processBuilder.command("sh", "-c", command);
            }

            Process process = processBuilder.start();

            // 读取命令输出
            StringBuilder output = new StringBuilder();
            StringBuilder errorOutput = new StringBuilder();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                 BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }

                while ((line = errorReader.readLine()) != null) {
                    errorOutput.append(line).append("\n");
                }
            }

            // 等待命令执行完成
            Integer timeout = devOpsConfig.getAppRestart().getCommandTimeout();
            boolean finished = process.waitFor(timeout, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                detail.setSuccess(false);
                detail.setErrorMessage("命令执行超时");
                log.error("重启命令执行超时: {}", command);
            } else {
                int exitCode = process.exitValue();
                detail.setSuccess(exitCode == 0);

                if (exitCode == 0) {
                    detail.setCommandOutput(output.toString());
                    log.info("应用 {} 重启成功", appName);
                } else {
                    detail.setErrorMessage("命令执行失败，退出码: " + exitCode);
                    detail.setCommandOutput(errorOutput.toString());
                    log.error("应用 {} 重启失败，退出码: {}，错误输出: {}", appName, exitCode, errorOutput.toString());
                }
            }

        } catch (Exception e) {
            detail.setSuccess(false);
            detail.setErrorMessage("执行重启命令时发生异常: " + e.getMessage());
            log.error("执行重启命令时发生异常: {}", e.getMessage(), e);
        } finally {
            detail.setExecutionTime(System.currentTimeMillis() - startTime);
        }

        return detail;
    }

    @Override
    public Boolean isRestartEnabled() {
        return devOpsConfig.getAppRestart().getEnabled();
    }

    @Override
    public String getAppNameByDirectory(String directory) {
        if (!isRestartEnabled()) {
            return null;
        }

        Map<String, String> directoryAppMapping = devOpsConfig.getAppRestart().getDirectoryAppMapping();
        if (directoryAppMapping == null) {
            return null;
        }

        return directoryAppMapping.get(directory);
    }

    @Override
    public RestartResultVO autoRestartAfterSync(CompareReportVO compareReport, Boolean syncSuccess, String clientIp) {
        if (!isRestartEnabled()) {
            log.debug("应用重启功能未启用");
            return null;
        }

        if (!syncSuccess) {
            log.warn("文件同步失败，跳过应用重启");
            return null;
        }

        if (compareReport == null || !compareReport.getHasDifferences()) {
            log.debug("没有文件变化，无需重启应用");
            return null;
        }

        Set<String> appsToRestart = determineAppsToRestart(compareReport);
        if (appsToRestart.isEmpty()) {
            log.debug("没有应用需要重启");
            return null;
        }

        return restartApps(appsToRestart, clientIp);
    }

    /**
     * 检查文件是否在指定目录下
     */
    private boolean isFileInDirectory(String filePath, String directory) {
        if (StrUtil.isBlank(filePath) || StrUtil.isBlank(directory)) {
            return false;
        }

        try {
            Path file = Paths.get(filePath).normalize();
            Path dir = Paths.get(directory).normalize();
            return file.startsWith(dir);
        } catch (Exception e) {
            log.warn("检查文件路径时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据应用名称获取对应的目录
     */
    private String getDirectoryByAppName(String appName) {
        Map<String, String> directoryAppMapping = devOpsConfig.getAppRestart().getDirectoryAppMapping();
        if (directoryAppMapping == null) {
            return null;
        }

        return directoryAppMapping.entrySet().stream()
                .filter(entry -> appName.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }
}
