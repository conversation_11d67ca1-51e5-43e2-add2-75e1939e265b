package com.rs.module.devops.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.devops.config.DevOpsConfig;
import com.rs.module.devops.service.AppRestartService;
import com.rs.module.devops.service.DevOpsClientService;
import com.rs.module.devops.util.FileHashUtil;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.FileInfoVO;
import com.rs.module.devops.vo.RestartResultVO;
import com.rs.module.devops.vo.SyncProgressVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Stream;
import java.util.zip.ZipFile;

/**
 * DevOps客户端服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DevOpsClientServiceImpl implements DevOpsClientService {

    @Autowired
    private DevOpsConfig devOpsConfig;

    @Autowired
    private AppRestartService appRestartService;

    @Override
    public List<FileInfoVO> scanLocalFiles() {
        log.info("开始扫描本地文件...");
        List<FileInfoVO> fileInfoList = new ArrayList<>();

        List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
        if (scanDirectories == null || scanDirectories.isEmpty()) {
            log.warn("未配置扫描目录");
            return fileInfoList;
        }

        for (String directory : scanDirectories) {
            try {
                scanDirectory(directory, fileInfoList);
            } catch (Exception e) {
                log.error("扫描目录 {} 时发生错误: {}", directory, e.getMessage(), e);
            }
        }

        log.info("本地文件扫描完成，共扫描到 {} 个文件", fileInfoList.size());
        return fileInfoList;
    }

    private void scanDirectory(String directoryPath, List<FileInfoVO> fileInfoList) {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path) || !Files.isDirectory(path)) {
            log.warn("目录不存在或不是有效目录: {}", directoryPath);
            return;
        }

        try (Stream<Path> paths = Files.walk(path)) {
            paths.filter(Files::isRegularFile)
                    .filter(this::shouldIncludeFile)
                    .forEach(filePath -> {
                        try {
                            FileInfoVO fileInfo = createFileInfo(filePath);
                            if (fileInfo != null) {
                                fileInfoList.add(fileInfo);
                            }
                        } catch (Exception e) {
                            log.error("处理文件 {} 时发生错误: {}", filePath, e.getMessage());
                        }
                    });
        } catch (IOException e) {
            log.error("遍历目录 {} 时发生错误: {}", directoryPath, e.getMessage(), e);
        }
    }

    private boolean shouldIncludeFile(Path filePath) {
        String fileName = filePath.getFileName().toString();

        // 检查文件扩展名
        List<String> fileExtensions = devOpsConfig.getJarScan().getFileExtensions();
        if (fileExtensions != null && !fileExtensions.isEmpty()) {
            boolean hasValidExtension = fileExtensions.stream()
                    .anyMatch(ext -> fileName.toLowerCase().endsWith(ext.toLowerCase()));
            if (!hasValidExtension) {
                return false;
            }
        }

        // 检查排除目录
        List<String> excludeDirectories = devOpsConfig.getJarScan().getExcludeDirectories();
        if (excludeDirectories != null) {
            String pathStr = filePath.toString();
            for (String excludeDir : excludeDirectories) {
                if (pathStr.contains(excludeDir)) {
                    return false;
                }
            }
        }

        // 检查文件大小
        Long maxFileSize = devOpsConfig.getJarScan().getMaxFileSize();
        if (maxFileSize != null && maxFileSize > 0) {
            try {
                long fileSize = Files.size(filePath);
                if (fileSize > maxFileSize) {
                    return false;
                }
            } catch (IOException e) {
                log.warn("无法获取文件大小: {}", filePath);
                return false;
            }
        }

        return true;
    }

    private FileInfoVO createFileInfo(Path filePath) {
        try {
            File file = filePath.toFile();
            FileInfoVO fileInfo = new FileInfoVO();

            fileInfo.setFilePath(file.getAbsolutePath());
            fileInfo.setFileName(file.getName());
            fileInfo.setFileSize(file.length());
            fileInfo.setLastModified(new Date(file.lastModified()));
            fileInfo.setIsDirectory(file.isDirectory());

            // 设置相对路径
            String relativePath = getRelativePath(file.getAbsolutePath());
            fileInfo.setRelativePath(relativePath);

            // 设置文件扩展名
            String extension = FileUtil.extName(file.getName());
            fileInfo.setFileExtension(StrUtil.isNotBlank(extension) ? "." + extension : "");

            // 计算哈希值
            if (devOpsConfig.getJarScan().getCalculateMd5()) {
                fileInfo.setMd5Hash(FileHashUtil.calculateMD5(file));
            }
            if (devOpsConfig.getJarScan().getCalculateSha1()) {
                fileInfo.setSha1Hash(FileHashUtil.calculateSHA1(file));
            }

            return fileInfo;
        } catch (Exception e) {
            log.error("创建文件信息时发生错误: {}", filePath, e);
            return null;
        }
    }

    private String getRelativePath(String absolutePath) {
        List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
        for (String baseDir : scanDirectories) {
            try {
                Path basePath = Paths.get(baseDir).toAbsolutePath().normalize();
                Path filePath = Paths.get(absolutePath).toAbsolutePath().normalize();
                if (filePath.startsWith(basePath)) {
                    return basePath.relativize(filePath).toString();
                }
            } catch (Exception e) {
                log.debug("计算相对路径时发生错误: {}", e.getMessage());
            }
        }
        return absolutePath;
    }

    @Override
    public CompareReportVO submitScanResult(List<FileInfoVO> fileInfoList) {
        log.info("提交扫描结果到服务端，文件数量: {}", fileInfoList.size());

        try {
            String serverUrl = devOpsConfig.getFileSync().getServerUrl();
            String url = serverUrl + "/devops/server/compare";

            Map<String, Object> params = new HashMap<>();
            params.put("clientFiles", fileInfoList);
            params.put("clientIp", getClientIp());

            Integer timeout = devOpsConfig.getFileSync().getDownloadTimeout() * 1000;

            String response = HttpUtil.createPost(url)
                    .body(JSONUtil.toJsonStr(params))
                    .contentType("application/json")
                    .timeout(timeout)
                    .execute()
                    .body();
            CommonResult result = JSONUtil.toBean(response, CommonResult.class);

            if (result.isSuccess()) {
                CompareReportVO compareReport = JSONUtil.toBean(JSONUtil.toJsonStr(result.getData()), CompareReportVO.class);
                log.info("服务端对比完成: {}", compareReport.getSummary());
                return compareReport;
            } else {
                log.error("服务端对比失败: {}", result.getMsg());
                return null;
            }
        } catch (Exception e) {
            log.error("提交扫描结果时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Boolean downloadFile(FileInfoVO fileInfo) {
        log.info("开始下载文件: {}", fileInfo.getRelativePath());

        try {
            String serverUrl = devOpsConfig.getFileSync().getServerUrl();
            String url = serverUrl + "/devops/server/download?filePath=" + fileInfo.getFilePath();

            byte[] fileContent = HttpUtil.downloadBytes(url);
            if (fileContent == null || fileContent.length == 0) {
                log.error("下载文件内容为空: {}", fileInfo.getFilePath());
                return false;
            }

            // 写入本地文件
            String localPath = fileInfo.getFilePath();

            // 确保父目录存在（虽然FileUtil.writeBytes会自动创建，但显式创建更安全）
            File localFile = new File(localPath);
            File parentDir = localFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (created) {
                    log.info("自动创建目录: {}", parentDir.getAbsolutePath());
                } else {
                    log.warn("创建目录失败: {}", parentDir.getAbsolutePath());
                }
            }

            FileUtil.writeBytes(fileContent, localPath);

            // 验证文件完整性
            if (StrUtil.isNotBlank(fileInfo.getMd5Hash())) {
                if (!verifyFileIntegrity(localPath, fileInfo.getMd5Hash())) {
                    log.error("文件完整性验证失败: {}", fileInfo.getFilePath());
                    return false;
                }
            }

            log.info("文件下载成功: {}", fileInfo.getFilePath());

            // 如果是web.zip文件，需要进行解压处理
            if (fileInfo.getRelativePath().toLowerCase().endsWith("web.zip")) {
                log.info("检测到web.zip文件，开始解压处理: {}", fileInfo.getFilePath());
                boolean extractSuccess = extractWebZipFile(localPath, fileInfo.getFilePath());
                if (!extractSuccess) {
                    log.error("web.zip文件解压失败: {}", fileInfo.getFilePath());
                    return false;
                }
                log.info("web.zip文件解压完成: {}", fileInfo.getFilePath());
            }

            return true;
        } catch (Exception e) {
            log.error("下载文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }


    @Override
    public String backupFile(FileInfoVO fileInfo) {
        try {
            String originalPath = fileInfo.getFilePath();
            if (!FileUtil.exist(originalPath)) {
                return null;
            }

            String timestamp = DateUtil.format(new Date(), "yyyyMMdd");
            String backupPath = originalPath + "." + timestamp + ".bak";

            FileUtil.copy(originalPath, backupPath, true);
            log.info("文件备份成功: {} -> {}", originalPath, backupPath);
            return backupPath;
        } catch (Exception e) {
            log.error("备份文件时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Boolean executeSync(CompareReportVO compareReport) {
        // 使用带进度跟踪的同步方法
        return executeSyncWithProgress(compareReport);
    }

    @Override
    public Integer cleanExpiredBackups() {
        log.info("开始清理过期备份文件...");
        int cleanedCount = 0;

        try {
            Integer retentionDays = devOpsConfig.getFileSync().getBackupRetentionDays();
            if (retentionDays == null || retentionDays <= 0) {
                return 0;
            }

            Date cutoffTime = new Date(System.currentTimeMillis() - retentionDays * 24 * 60 * 60 * 1000L);

            List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
            for (String directory : scanDirectories) {
                cleanedCount += cleanBackupsInDirectory(directory, cutoffTime);
            }

            log.info("清理过期备份文件完成，共清理 {} 个文件", cleanedCount);
        } catch (Exception e) {
            log.error("清理过期备份文件时发生错误: {}", e.getMessage(), e);
        }

        return cleanedCount;
    }

    private int cleanBackupsInDirectory(String directory, Date cutoffTime) {
        int count = 0;
        try {
            Path path = Paths.get(directory);
            if (!Files.exists(path)) {
                return 0;
            }

            try (Stream<Path> paths = Files.walk(path)) {
                paths.filter(Files::isRegularFile)
                        .filter(p -> p.getFileName().toString().endsWith(".bak"))
                        .forEach(backupPath -> {
                            try {
                                Date fileTime = new Date(Files.getLastModifiedTime(backupPath).toMillis());
                                if (fileTime.before(cutoffTime)) {
                                    Files.delete(backupPath);
                                    log.debug("删除过期备份文件: {}", backupPath);
                                }
                            } catch (Exception e) {
                                log.warn("删除备份文件时发生错误: {}", e.getMessage());
                            }
                        });
            }
        } catch (Exception e) {
            log.error("清理目录 {} 中的备份文件时发生错误: {}", directory, e.getMessage());
        }
        return count;
    }

    @Override
    public String getClientIp() {
        try {
            // 优先级列表：192开头的IP > 14开头的IP > 其他内网IP > 本地回环
            String preferredIp = null;
            String fallbackIp = null;

            // 遍历所有网络接口
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();

                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                // 遍历该接口的所有IP地址
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();

                    // 跳过IPv6地址和回环地址
                    if (address.isLoopbackAddress() || address.getHostAddress().contains(":")) {
                        continue;
                    }

                    String ip = address.getHostAddress();
                    log.debug("发现网络接口 {} 的IP地址: {}", networkInterface.getName(), ip);

                    // 优先选择192开头的IP
                    if (ip.startsWith("192.")) {
                        log.info("选择192开头的IP地址: {}", ip);
                        return ip;
                    }

                    // 其次选择14开头的IP
                    if (ip.startsWith("14.")) {
                        preferredIp = ip;
                        log.debug("找到14开头的IP地址: {}", ip);
                    }

                    // 备选：其他内网IP（10.x.x.x, 172.16-31.x.x）
                    if (fallbackIp == null && (ip.startsWith("10.") ||
                            (ip.startsWith("172.") && isPrivate172(ip)))) {
                        fallbackIp = ip;
                        log.debug("找到其他内网IP地址: {}", ip);
                    }
                }
            }

            // 返回找到的最优IP
            if (preferredIp != null) {
                log.info("选择14开头的IP地址: {}", preferredIp);
                return preferredIp;
            }

            if (fallbackIp != null) {
                log.info("选择内网IP地址: {}", fallbackIp);
                return fallbackIp;
            }

            // 如果都没找到，使用默认方法
            String defaultIp = InetAddress.getLocalHost().getHostAddress();
            log.info("使用默认IP地址: {}", defaultIp);
            return defaultIp;

        } catch (Exception e) {
            log.warn("获取客户端IP失败: {}", e.getMessage());
            return "unknown";
        }
    }

    /**
     * 判断是否为172.16-172.31范围的私有IP
     */
    private boolean isPrivate172(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length == 4 && "172".equals(parts[0])) {
                int secondOctet = Integer.parseInt(parts[1]);
                return secondOctet >= 16 && secondOctet <= 31;
            }
        } catch (Exception e) {
            log.debug("解析172网段IP失败: {}", ip);
        }
        return false;
    }

    @Override
    public Boolean verifyFileIntegrity(String filePath, String expectedMd5) {
        try {
            String actualMd5 = FileHashUtil.calculateMD5(new File(filePath));
            return expectedMd5.equalsIgnoreCase(actualMd5);
        } catch (Exception e) {
            log.error("验证文件完整性时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public RestartResultVO executeSyncWithRestart(CompareReportVO compareReport) {
        log.info("开始执行文件同步并自动重启相关应用...");

        // 1. 执行文件同步
        Boolean syncSuccess = executeSync(compareReport);
        log.info("文件同步结果: {}", syncSuccess ? "成功" : "失败");

        // 2. 如果同步成功且启用了应用重启功能，则自动重启相关应用
        if (syncSuccess && appRestartService.isRestartEnabled()) {
            String clientIp = getClientIp();
            RestartResultVO restartResult = appRestartService.autoRestartAfterSync(compareReport, syncSuccess, clientIp);

            if (restartResult != null) {
                log.info("应用重启完成: {}", restartResult.getSummary());

                // 向服务端提交重启日志
                submitRestartLog(restartResult);

                return restartResult;
            } else {
                log.debug("无应用需要重启");
            }
        }

        return null;
    }

    @Override
    public Boolean submitSyncStatus(Boolean syncResult) {
        try {
            String serverUrl = devOpsConfig.getFileSync().getServerUrl();
            if (StrUtil.isBlank(serverUrl)) {
                log.warn("未配置服务端地址，无法提交同步状态");
                return false;
            }

            String clientIp = getClientIp();
            String url = serverUrl + "/devops/server/sync-status";

            Map<String, Object> params = new HashMap<>();
            params.put("clientIp", clientIp);
            params.put("syncResult", syncResult);

            log.info("向服务端提交同步状态: {}, 结果: {}", clientIp, syncResult ? "成功" : "失败");

            String response = HttpUtil.post(url, params);
            CommonResult<?> result = JSONUtil.toBean(response, CommonResult.class);

            if (result.getCode() == 0) {
                log.info("同步状态提交成功");
                return true;
            } else {
                log.error("同步状态提交失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("提交同步状态时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean submitRestartLog(RestartResultVO restartResult) {
        try {
            String serverUrl = devOpsConfig.getFileSync().getServerUrl();
            if (StrUtil.isBlank(serverUrl)) {
                log.warn("未配置服务端地址，无法提交重启日志");
                return false;
            }

            if (restartResult == null) {
                log.debug("重启结果为空，无需提交");
                return true;
            }

            String url = serverUrl + "/devops/server/restart-log";

            log.info("向服务端提交重启日志");

            String jsonData = JSONUtil.toJsonStr(restartResult);
            String response = HttpUtil.createPost(url)
                    .body(jsonData)
                    .contentType("application/json")
                    .execute()
                    .body();
            CommonResult<?> result = JSONUtil.toBean(response, CommonResult.class);

            if (result.getCode() == 0) {
                log.info("重启日志提交成功");
                return true;
            } else {
                log.error("重启日志提交失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("提交重启日志时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean submitSyncProgress(SyncProgressVO syncProgress) {
        try {
            String serverUrl = devOpsConfig.getFileSync().getServerUrl();
            if (StrUtil.isBlank(serverUrl)) {
                log.warn("未配置服务端地址，无法提交同步进度");
                return false;
            }

            if (syncProgress == null) {
                log.debug("同步进度为空，无需提交");
                return true;
            }

            String url = serverUrl + "/devops/server/sync-progress";

            log.info("向服务端提交同步进度: {}%", syncProgress.getProgressPercentage());

            String jsonData = JSONUtil.toJsonStr(syncProgress);
            String response = HttpUtil.createPost(url)
                    .body(jsonData)
                    .contentType("application/json")
                    .execute()
                    .body();
            CommonResult<?> result = JSONUtil.toBean(response, CommonResult.class);

            if (result.getCode() == 0) {
                log.debug("同步进度提交成功");
                return true;
            } else {
                log.error("同步进度提交失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("提交同步进度时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean executeSyncWithProgress(CompareReportVO compareReport) {
        log.info("开始执行带进度跟踪的文件同步...");

        String clientIp = getClientIp();
        SyncProgressVO syncProgress = new SyncProgressVO(clientIp);

        // 初始化同步进度
        initializeSyncProgress(syncProgress, compareReport);
        submitSyncProgress(syncProgress);

        boolean success = true;

        try {
            // 处理需要更新的文件
            if (compareReport.getFilesToUpdate() != null) {
                success &= processFiles(compareReport.getFilesToUpdate(), "UPDATE", syncProgress);
            }

            // 处理需要新增的文件
            if (compareReport.getFilesToAdd() != null) {
                success &= processFiles(compareReport.getFilesToAdd(), "ADD", syncProgress);
            }

            // 处理需要删除的文件
            if (compareReport.getFilesToDelete() != null) {
                success &= processFiles(compareReport.getFilesToDelete(), "DELETE", syncProgress);
            }

            // 完成同步
            syncProgress.setSyncEndTime(new Date());
            syncProgress.setSyncStatus(success ? "COMPLETED" : "FAILED");
            syncProgress.calculateProgress();
            syncProgress.calculateSyncSpeed();
            syncProgress.generateSummary();

            // 提交最终进度
            submitSyncProgress(syncProgress);

            log.info("带进度跟踪的文件同步完成，结果: {}", success ? "成功" : "部分失败");
            return success;

        } catch (Exception e) {
            log.error("执行同步时发生错误: {}", e.getMessage(), e);

            // 更新错误状态
            syncProgress.setSyncEndTime(new Date());
            syncProgress.setSyncStatus("FAILED");
            syncProgress.setErrorMessage(e.getMessage());
            syncProgress.generateSummary();
            submitSyncProgress(syncProgress);

            return false;
        }
    }

    /**
     * 初始化同步进度
     */
    private void initializeSyncProgress(SyncProgressVO syncProgress, CompareReportVO compareReport) {
        List<SyncProgressVO.FileProgressVO> fileDetails = new ArrayList<>();

        // 添加需要更新的文件
        if (compareReport.getFilesToUpdate() != null) {
            for (FileInfoVO fileInfo : compareReport.getFilesToUpdate()) {
                SyncProgressVO.FileProgressVO fileProgress = new SyncProgressVO.FileProgressVO();
                fileProgress.setRelativePath(fileInfo.getRelativePath());
                fileProgress.setFileSize(fileInfo.getFileSize());
                fileProgress.setOperation("UPDATE");
                fileProgress.setStatus("PENDING");
                fileDetails.add(fileProgress);
            }
        }

        // 添加需要新增的文件
        if (compareReport.getFilesToAdd() != null) {
            for (FileInfoVO fileInfo : compareReport.getFilesToAdd()) {
                SyncProgressVO.FileProgressVO fileProgress = new SyncProgressVO.FileProgressVO();
                fileProgress.setRelativePath(fileInfo.getRelativePath());
                fileProgress.setFileSize(fileInfo.getFileSize());
                fileProgress.setOperation("ADD");
                fileProgress.setStatus("PENDING");
                fileDetails.add(fileProgress);
            }
        }

        // 添加需要删除的文件
        if (compareReport.getFilesToDelete() != null) {
            for (FileInfoVO fileInfo : compareReport.getFilesToDelete()) {
                SyncProgressVO.FileProgressVO fileProgress = new SyncProgressVO.FileProgressVO();
                fileProgress.setRelativePath(fileInfo.getRelativePath());
                fileProgress.setFileSize(fileInfo.getFileSize());
                fileProgress.setOperation("DELETE");
                fileProgress.setStatus("PENDING");
                fileDetails.add(fileProgress);
            }
        }

        syncProgress.setFileDetails(fileDetails);
        syncProgress.setTotalFiles(fileDetails.size());
        syncProgress.setPendingFiles(fileDetails.size());
        syncProgress.setCompletedFiles(0);
        syncProgress.setFailedFiles(0);
        syncProgress.setSkippedFiles(0);
        syncProgress.setSyncStatus("PROCESSING");
        syncProgress.calculateProgress();
        syncProgress.generateSummary();
    }

    /**
     * 处理文件列表
     */
    private boolean processFiles(List<FileInfoVO> files, String operation, SyncProgressVO syncProgress) {
        boolean success = true;

        for (FileInfoVO fileInfo : files) {
            String relativePath = fileInfo.getRelativePath();
            syncProgress.setCurrentFile(relativePath);

            // 更新文件状态为处理中
            syncProgress.updateFileStatus(relativePath, "PROCESSING", null);
            submitSyncProgress(syncProgress);

            try {
                boolean fileSuccess = false;

                switch (operation) {
                    case "UPDATE":
                        // 备份原文件
                        backupFile(fileInfo);
                        // 下载新文件
                        fileSuccess = downloadFile(fileInfo);
                        break;
                    case "ADD":
                        // 下载新文件
                        fileSuccess = downloadFile(fileInfo);
                        break;
                    case "DELETE":
                        // 备份后删除
                        String localPath = fileInfo.getFilePath();
                        if (FileUtil.exist(localPath)) {
                            backupFile(fileInfo);
                            FileUtil.del(localPath);
                            fileSuccess = true;
                        } else {
                            fileSuccess = true; // 文件不存在，视为删除成功
                        }
                        break;
                }

                // 更新文件状态
                if (fileSuccess) {
                    syncProgress.updateFileStatus(relativePath, "COMPLETED", null);
                    log.info("文件 {} 处理成功", relativePath);
                } else {
                    syncProgress.updateFileStatus(relativePath, "FAILED", "处理失败");
                    log.error("文件 {} 处理失败", relativePath);
                    success = false;
                }

            } catch (Exception e) {
                syncProgress.updateFileStatus(relativePath, "FAILED", e.getMessage());
                log.error("处理文件 {} 时发生错误: {}", relativePath, e.getMessage(), e);
                success = false;
            }

            // 提交进度更新
            submitSyncProgress(syncProgress);
        }

        return success;
    }

    /**
     * 解压web.zip文件
     */
    private boolean extractWebZipFile(String zipFilePath, String targetPath) {
        try {
            log.info("开始解压web.zip文件: {}", zipFilePath);

            // 在解压前，先对目标目录进行备份
            File targetFile = new File(targetPath);
            File targetDir = targetFile.isDirectory() ? targetFile : targetFile.getParentFile();
            if (targetDir != null && targetDir.exists() && targetDir.isDirectory()) {
                String backupPath = backupDirectory(targetDir.getAbsolutePath());
                if (StrUtil.isBlank(backupPath) || !new File(backupPath).exists()) {
                    log.error("解压前备份目录失败，终止解压。目标目录: {}", targetDir.getAbsolutePath());
                    return false;
                }
                log.info("解压前备份完成: {} -> {}", targetDir.getAbsolutePath(), backupPath);
            } else {
                log.info("目标目录不存在或无效，跳过解压前备份。目标: {}", targetPath);
            }

            // 解压并处理
            boolean extractSuccess = extractWebZipToTarget(zipFilePath, targetPath);

            if (extractSuccess) {
                log.info("web.zip文件解压成功: {}", targetPath);
                return true;
            } else {
                log.error("web.zip文件解压失败: {}", targetPath);
                return false;
            }

        } catch (Exception e) {
            log.error("解压web.zip文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解压web.zip文件到目标位置
     */
    private boolean extractWebZipToTarget(String zipFilePath, String targetPath) {
        try {
            // 创建临时解压目录
            String tempExtractDir = "/home/<USER>" + System.currentTimeMillis();
            File tempDir = new File(tempExtractDir);
            if (!tempDir.mkdirs()) {
                log.error("创建临时解压目录失败: {}", tempExtractDir);
                return false;
            }

            // 解压到临时目录
            try {
                ZipFile tempZipFile = new ZipFile(zipFilePath, Charset.forName("UTF-8"));
                ZipUtil.unzip(tempZipFile, new File(tempExtractDir));
            } catch (Exception e) {

                ZipFile tempZipFile = new ZipFile(zipFilePath, Charset.forName("GBK"));
                ZipUtil.unzip(tempZipFile, new File(tempExtractDir));
            }

            // 分析解压后的目录结构
            File[] extractedFiles = tempDir.listFiles();
            if (extractedFiles == null || extractedFiles.length == 0) {
                log.error("解压后的目录为空");
                FileUtil.del(tempDir);
                return false;
            }

            // 确定目标解压路径
            if (targetPath == null) {
                log.error("无法确定目标解压路径");
                FileUtil.del(tempDir);
                return false;
            }

            // 确保目标目录存在
            File targetDir = new File(targetPath);
            if (!targetDir.exists() && !targetDir.mkdirs()) {
                log.error("创建目标目录失败: {}", targetPath);
                FileUtil.del(tempDir);
                return false;
            }

            // 复制文件到目标位置
            boolean copySuccess = copyExtractedFiles(extractedFiles, targetPath);

            // 清理临时目录
            FileUtil.del(tempDir);

            return copySuccess;

        } catch (Exception e) {
            log.error("解压web.zip文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 确定web.zip的目标解压路径
     */
    private String determineTargetPath(String originalZipPath, File[] extractedFiles) {
        try {
            // 获取原始zip文件的绝对路径（不包含文件名）
            String zipDir = new File(originalZipPath).getParent();

            log.info("原始zip文件目录: {}", zipDir);

            // 检查解压后的内容结构
            if (extractedFiles.length == 1 && extractedFiles[0].isDirectory()) {
                // 情况1: 压缩包内只有一个文件夹（如acp-web文件夹）
                String folderName = extractedFiles[0].getName();
                log.info("检测到单一文件夹结构: {}", folderName);

                // 解压到当前目录，保持文件夹结构
                return zipDir;
            } else {
                // 情况2: 压缩包内直接包含文件（如index.html等）
                boolean hasIndexHtml = false;
                for (File file : extractedFiles) {
                    if ("index.html".equalsIgnoreCase(file.getName())) {
                        hasIndexHtml = true;
                        break;
                    }
                }

                if (hasIndexHtml) {
                    log.info("检测到直接包含index.html的结构");

                    // 从zip文件名推断目标文件夹名
                    String zipFileName = new File(originalZipPath).getName();
                    String targetFolderName = zipFileName.substring(0, zipFileName.lastIndexOf(".zip"));

                    // 解压到对应的文件夹下
                    return Paths.get(zipDir, targetFolderName).toString();
                } else {
                    log.info("检测到其他文件结构，解压到当前目录");
                    return zipDir;
                }
            }

        } catch (Exception e) {
            log.error("确定目标路径时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 复制解压后的文件到目标位置
     */
    private boolean copyExtractedFiles(File[] extractedFiles, String targetPath) {
        try {
            for (File file : extractedFiles) {
                if (file.isDirectory()) {
                    // 递归复制目录，需要检查目录内的文件
                    copyDirectoryWithProtection(file, targetPath);
                } else {
                    // 复制单个文件，检查是否为受保护文件
                    copyFileWithProtection(file, targetPath);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("复制解压文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 复制目录，保护特定文件不被覆盖
     */
    private void copyDirectoryWithProtection(File sourceDir, String targetBasePath) {
        try {
            String targetDirPath = Paths.get(targetBasePath, sourceDir.getName()).toString();
            File targetDir = new File(targetDirPath);

            // 确保目标目录存在
            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }

            // 递归处理目录中的所有文件和子目录
            File[] files = sourceDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        // 递归处理子目录
                        copyDirectoryWithProtection(file, targetDirPath);
                    } else {
                        // 处理文件
                        copyFileWithProtection(file, targetDirPath);
                    }
                }
            }

            log.info("复制目录: {} -> {}", sourceDir.getAbsolutePath(), targetDirPath);
        } catch (Exception e) {
            log.error("复制目录时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 复制文件，保护特定文件不被覆盖
     */
    private void copyFileWithProtection(File sourceFile, String targetDirPath) {
        try {
            String fileName = sourceFile.getName();
            String targetFilePath = Paths.get(targetDirPath, fileName).toString();
            File targetFile = new File(targetFilePath);

            // 检查是否为受保护的文件
            if (isProtectedFile(fileName)) {
                if (targetFile.exists()) {
                    log.info("跳过受保护文件（已存在）: {}", targetFilePath);
                    return;
                } else {
                    log.info("复制受保护文件（目标不存在）: {} -> {}", sourceFile.getAbsolutePath(), targetFilePath);
                }
            }

            // 复制文件
            FileUtil.copy(sourceFile, targetFile, true);
            log.info("复制文件: {} -> {}", sourceFile.getAbsolutePath(), targetFilePath);

        } catch (Exception e) {
            log.error("复制文件时发生错误: {}", e.getMessage(), e);
        }
    }


    /**
     * 目录备份：将整个目录打包为zip，命名为 目录名.pre_extract_yyyyMMddHHmmss.zip
     * 返回备份文件的完整路径，失败返回null
     */
    private String backupDirectory(String dirPath) {
        try {
            if (StrUtil.isBlank(dirPath)) {
                return null;
            }
            File dir = new File(dirPath);
            if (!dir.exists() || !dir.isDirectory()) {
                log.warn("备份目录不存在或不是目录: {}", dirPath);
                return null;
            }

            String timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            String parent = dir.getParent();
            String name = dir.getName();
            String backupName = name + ".pre_extract_" + timestamp + ".zip";
            String backupPath = parent != null ? Paths.get(parent, backupName).toString()
                    : dirPath + ".pre_extract_" + timestamp + ".zip";

            // 执行压缩备份
            ZipUtil.zip(dirPath, backupPath);
            log.info("目录备份成功: {} -> {}", dirPath, backupPath);
            return backupPath;
        } catch (Exception e) {
            log.error("备份目录时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查是否为受保护的文件
     */
    private boolean isProtectedFile(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();

        // 受保护的文件列表
        return "base.js".equals(lowerFileName) || "nginx.conf".equals(lowerFileName);
    }

    @Override
    public List<String> rollbackFilesByDateSuffix(String dateSuffix) {
        log.info("开始根据日期后缀 {} 回退文件", dateSuffix);
        List<String> rollbackResults = new ArrayList<>();
        String clientIp = getClientIp();

        if (StrUtil.isBlank(dateSuffix)) {
            String errorMsg = "日期后缀不能为空";
            log.error("客户端 {} - {}", clientIp, errorMsg);
            rollbackResults.add("错误: " + errorMsg);
            return rollbackResults;
        }

        // 验证日期格式（应该是8位数字）
        if (!dateSuffix.matches("\\d{8}")) {
            String errorMsg = "日期后缀格式不正确，应为8位数字格式，如：20250801";
            log.error("客户端 {} - {}", clientIp, errorMsg);
            rollbackResults.add("错误: " + errorMsg);
            return rollbackResults;
        }

        List<String> scanDirectories = devOpsConfig.getJarScan().getScanDirectories();
        if (scanDirectories == null || scanDirectories.isEmpty()) {
            String errorMsg = "未配置扫描目录";
            log.error("客户端 {} - {}", clientIp, errorMsg);
            rollbackResults.add("错误: " + errorMsg);
            return rollbackResults;
        }

        int totalRollbackCount = 0;
        int successCount = 0;
        int failedCount = 0;

        for (String directory : scanDirectories) {
            try {
                log.info("客户端 {} - 在目录 {} 中查找日期后缀为 {} 的备份文件", clientIp, directory, dateSuffix);
                List<String> directoryResults = rollbackFilesInDirectory(directory, dateSuffix, clientIp);
                rollbackResults.addAll(directoryResults);

                // 统计结果
                for (String result : directoryResults) {
                    totalRollbackCount++;
                    if (result.startsWith("成功")) {
                        successCount++;
                    } else if (result.startsWith("失败")) {
                        failedCount++;
                    }
                }
            } catch (Exception e) {
                String errorMsg = String.format("处理目录 %s 时发生错误: %s", directory, e.getMessage());
                log.error("客户端 {} - {}", clientIp, errorMsg, e);
                rollbackResults.add("错误: " + errorMsg);
                failedCount++;
            }
        }

        // 添加汇总信息
        String summary = String.format("回退操作完成 - 总计: %d, 成功: %d, 失败: %d",
                totalRollbackCount, successCount, failedCount);
        log.info("客户端 {} - {}", clientIp, summary);
        rollbackResults.add(0, summary);

        return rollbackResults;
    }

    /**
     * 在指定目录中回退文件
     */
    private List<String> rollbackFilesInDirectory(String directoryPath, String dateSuffix, String clientIp) {
        List<String> results = new ArrayList<>();
        Path path = Paths.get(directoryPath);

        if (!Files.exists(path) || !Files.isDirectory(path)) {
            String errorMsg = String.format("目录不存在或不是有效目录: %s", directoryPath);
            log.warn("客户端 {} - {}", clientIp, errorMsg);
            results.add("警告: " + errorMsg);
            return results;
        }

        try (Stream<Path> paths = Files.walk(path)) {
            paths.filter(Files::isRegularFile)
                    .filter(filePath -> {
                        String fileName = filePath.getFileName().toString();
                        return fileName.endsWith("." + dateSuffix + ".bak");
                    })
                    .forEach(backupFilePath -> {
                        try {
                            String result = rollbackSingleFile(backupFilePath, dateSuffix, clientIp);
                            results.add(result);
                        } catch (Exception e) {
                            String errorMsg = String.format("回退文件 %s 时发生错误: %s",
                                    backupFilePath.getFileName(), e.getMessage());
                            log.error("客户端 {} - {}", clientIp, errorMsg, e);
                            results.add("失败: " + errorMsg);
                        }
                    });
        } catch (IOException e) {
            String errorMsg = String.format("遍历目录 %s 时发生错误: %s", directoryPath, e.getMessage());
            log.error("客户端 {} - {}", clientIp, errorMsg, e);
            results.add("错误: " + errorMsg);
        }

        return results;
    }

    /**
     * 回退单个文件
     */
    private String rollbackSingleFile(Path backupFilePath, String dateSuffix, String clientIp) {
        try {
            String backupFileName = backupFilePath.getFileName().toString();

            // 计算原始文件名：移除 .日期.bak 后缀
            String originalFileName = backupFileName.substring(0,
                    backupFileName.length() - ("." + dateSuffix + ".bak").length());

            Path originalFilePath = backupFilePath.getParent().resolve(originalFileName);

            log.info("客户端 {} - 开始回退文件: {} -> {}", clientIp, backupFileName, originalFileName);

            // 检查备份文件是否存在
            if (!Files.exists(backupFilePath)) {
                String errorMsg = String.format("备份文件不存在: %s", backupFileName);
                log.error("客户端 {} - {}", clientIp, errorMsg);
                return "失败: " + errorMsg;
            }

            // 如果原始文件存在，先备份当前文件
            if (Files.exists(originalFilePath)) {
                String currentBackupName = originalFileName + ".before_rollback_" +
                        DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".bak";
                Path currentBackupPath = originalFilePath.getParent().resolve(currentBackupName);

                Files.copy(originalFilePath, currentBackupPath);
                log.info("客户端 {} - 当前文件已备份为: {}", clientIp, currentBackupName);
            }

            // 执行回退：复制备份文件到原始位置
            Files.copy(backupFilePath, originalFilePath,
                    java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            // 验证文件完整性（如果可能）
            long backupSize = Files.size(backupFilePath);
            long restoredSize = Files.size(originalFilePath);

            if (backupSize != restoredSize) {
                String errorMsg = String.format("文件大小不匹配，回退可能失败: 备份文件 %d bytes, 恢复文件 %d bytes",
                        backupSize, restoredSize);
                log.error("客户端 {} - {}", clientIp, errorMsg);
                return "失败: " + errorMsg;
            }

            String successMsg = String.format("文件回退成功: %s (大小: %d bytes)", originalFileName, restoredSize);
            log.info("客户端 {} - {}", clientIp, successMsg);
            return "成功: " + successMsg;

        } catch (Exception e) {
            String errorMsg = String.format("回退文件 %s 时发生异常: %s",
                    backupFilePath.getFileName(), e.getMessage());
            log.error("客户端 {} - {}", clientIp, errorMsg, e);
            return "失败: " + errorMsg;
        }
    }
}
