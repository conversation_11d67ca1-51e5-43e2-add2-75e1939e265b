package com.rs.module.devops.service;

import com.rs.module.devops.vo.ClientReportVO;
import com.rs.module.devops.vo.CompareReportVO;
import com.rs.module.devops.vo.RestartResultVO;
import com.rs.module.devops.vo.SyncProgressVO;

import java.util.List;
import java.util.Map;

/**
 * 客户端报告管理服务接口
 *
 * <AUTHOR>
 */
public interface ClientReportService {

    /**
     * 保存或更新客户端对比报告
     *
     * @param clientIp 客户端IP
     * @param compareReport 对比报告
     */
    void saveCompareReport(String clientIp, CompareReportVO compareReport);

    /**
     * 更新客户端同步状态
     *
     * @param clientIp 客户端IP
     * @param syncResult 同步结果
     */
    void updateSyncStatus(String clientIp, Boolean syncResult);

    /**
     * 添加客户端重启日志
     *
     * @param clientIp 客户端IP
     * @param restartResult 重启结果
     */
    void addRestartLog(String clientIp, RestartResultVO restartResult);

    /**
     * 获取指定客户端的报告
     *
     * @param clientIp 客户端IP
     * @return 客户端报告
     */
    ClientReportVO getClientReport(String clientIp);

    /**
     * 获取所有客户端报告
     *
     * @return 客户端报告列表
     */
    List<ClientReportVO> getAllClientReports();

    /**
     * 获取客户端报告摘要
     *
     * @return 客户端报告摘要Map，key为客户端IP，value为摘要信息
     */
    Map<String, Object> getClientReportSummary();

    /**
     * 清理过期的客户端报告
     *
     * @param retentionHours 保留小时数
     * @return 清理的报告数量
     */
    Integer cleanExpiredReports(Integer retentionHours);

    /**
     * 删除指定客户端的报告
     *
     * @param clientIp 客户端IP
     * @return 是否删除成功
     */
    Boolean removeClientReport(String clientIp);

    /**
     * 更新客户端同步进度
     *
     * @param clientIp 客户端IP
     * @param syncProgress 同步进度
     */
    void updateSyncProgress(String clientIp, SyncProgressVO syncProgress);

    /**
     * 获取客户端同步进度
     *
     * @param clientIp 客户端IP
     * @return 同步进度
     */
    SyncProgressVO getSyncProgress(String clientIp);

    /**
     * 暂停指定客户端的比较功能
     *
     * @param clientIp 客户端IP
     * @param reason 暂停原因
     * @return 是否暂停成功
     */
    Boolean pauseCompare(String clientIp, String reason);

    /**
     * 恢复指定客户端的比较功能
     *
     * @param clientIp 客户端IP
     * @return 是否恢复成功
     */
    Boolean resumeCompare(String clientIp);

    /**
     * 检查指定客户端是否被暂停比较
     *
     * @param clientIp 客户端IP
     * @return 是否被暂停
     */
    Boolean isComparePaused(String clientIp);
}
