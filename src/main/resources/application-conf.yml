conf:
  server:
    port:
      devops: 9919
  snowflake:
    worker-id: 1 		#全局唯一guid生成器终端ID,最大值为31，最小值为1
    datacenter-id: 2 	#全局唯一guid生成器数据中心ID,最大值为31，最小值为1
  system-mark: devops
  matchers:
    ignores: /doc.html/**,/swagger/**,/rpc-api/**,/v2/upload,/bsp/dic/**,/devops/**
  debug: false
  datasource:
    druid:
      log-slow-sql: true
      slow-sql-millis: 100
    dynamic:
      druid:
        initial-size: 1
        min-idle: 1 		# 最小连接池数量
        max-active: 20 		# 最大连接池数量
        max-wait: 600000 	# 配置获取连接等待超时的时间，单位：毫秒
      master:
        url: **************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
        username: postgres
        password: Go@123456
      bsp:
        url: ************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
        username: root
        password: sundun_bsp
  mongodb:
    uri: mongodb://*************:27111/bsp
    hosts:
  redis:
    host: *************
    port: 6399
    database: 3
    password: redisbsp
    timeout: 6000  # 连接超时时长（毫秒）
    max-redirects: 3
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

---
conf:
  nacos:
    enabled: true
    ip: *************
    port: 8848
    username: nacos
    password: nacos@gxx
    namespace: rs
    group: DEFAULT_GROUP
---
conf:
  dromara:
    x-file-storage:
      enable-storage: true
      access-key: admin
      secret-key: admin123456
      end-point: http://*************:9010
      bucket-name: devops
      domain: http://*************:9010/devops/
      base-path:

---
conf:
  bsp:
    token:
      url: http://*************:1910/oauth/token
---
conf:
  xxl:
    enabled: true
    admin:
      addresses: http://*************:8080/xxl-job-admin
      username: admin
      password: xxlbsp
    executor:
      ip:
      port: 9999
      logPath: logs/xxl-job/${spring.application.name}


# 使用Ribbon配置（默认情况下Feign使用Ribbon作为负载均衡器）
ribbon:
  ConnectTimeout: 5000     # 连接超时时间（毫秒）
  ReadTimeout: 60000       # 读取超时时间（毫秒，设置为1分钟）

# 或者直接使用Feign的配置（优先级更高）
feign:
  client:
    config:
      default:  # 全局配置
        connectTimeout: 5000
        readTimeout: 60000
---
########################## Jar文件扫描配置 ##########################
devops:
  # 运行模式：client（客户端）或 server（服务端）
  # 客户端模式：执行文件同步、备份清理等客户端任务
  # 服务端模式：执行文件扫描、统计信息等服务端任务
  run-mode: server
  jar-scan:
    # 默认扫描目录列表
    scan-directories:
      - "/release/rs-acp/"
      - "/release/acp-web/"
    # 是否启用递归扫描
    recursive: true
    # 文件扩展名过滤器
    file-extensions:
      - ".jar"
      - ".zip"
    # 排除的目录名称
    exclude-directories:
      - ".git"
      - ".svn"
      - "target"
      - "node_modules"
      - ".idea"
      - "dist"
      - "material"
      - "logs"
      - "backup"
      - "temp"
      - "ckeditor"
      - "css"
      - "downloads"
      - "fonts"
      - "image"
      - "js"
      - "uploads"
      - "docs"
      - "img"
    # 最大文件大小限制（字节），-1表示无限制
    max-file-size: -1
    # 是否计算SHA-1哈希值
    calculate-sha1: true
    # 是否计算MD5哈希值
    calculate-md5: true
  # 文件同步配置
  # 光驱扫描复制配置
  cdrom-copy:
    # 要监听的盘符列表（留空则自动猜测）
    drive-letters:
      - "E:"
    # 目标发布目录（Windows 示例）
    target-release-dir: "C:/release"
    # 是否仅复制当天日期相关内容
    only-today: true
    # 日期匹配格式
    date-patterns:
      - "yyyyMMdd"
      - "yyyy-MM-dd"
    # 包含的文件后缀（留空表示全部）
    include-extensions:
      - ".zip"
    # 是否覆盖已有文件
    overwrite: true

  # 发布目录上传配置
  release-upload:
    release-dir: "C:/release"
    include-extensions:
      - ".zip"
      - ".jar"
    keep-zip: false
    # 服务端保存目录，若不填默认 /release
    target-dir-on-server: "/release"


  file-sync:
    # 服务端地址
    server-url: http://*************:9919
    # 客户端扫描间隔（分钟）
    client-scan-interval: 30
    # 服务端扫描间隔（分钟）
    server-scan-interval: 30
    # 文件下载超时时间（秒）
    download-timeout: 300
    # 备份文件保留天数
    backup-retention-days: 7
    # 是否启用客户端自动同步
    enable-client-auto-sync: true
    # 是否启用服务端自动扫描
    enable-server-auto-scan: true
    # 最大重试次数
    max-retry-count: 3
    # 重试间隔（秒）
    retry-interval: 10
  # 应用重启配置
  app-restart:
    # 是否启用应用重启功能
    enabled: true
    # 是否在Docker环境中运行
    docker-mode: false
    # Docker主机命令前缀（Docker模式下使用）
    docker-host-command: ""
    # 命令执行超时时间（秒）
    command-timeout: 60
    # 目录与应用的映射关系
    directory-app-mapping:
      'rs-acp': '/release/rs-acp/'
      'acp-web': '/release/acp-web/'
    # 重启命令模板，{appName} 会被替换为实际的应用名称
    restart-command-template: "docker restart {appName}"
    # 是否启用重启前延迟
    enable-restart-delay: true
    # 重启前延迟时间（秒）
    restart-delay-seconds: 5
    # 是否记录重启日志
    enable-restart-log: true

  # 定时任务配置
  scheduled-tasks:
    # 客户端自动同步任务 cron 表达式（默认每30分钟执行一次）
    client-auto-sync-cron: "0 */1 * * * ?"
    # 清理过期备份文件任务 cron 表达式（默认每天凌晨2点执行）
    clean-expired-backups-cron: "0 0 2 * * ?"
    # 光驱扫描复制任务 cron 表达式（默认每5分钟执行一次）
    cdrom-scan-copy-cron: "0 */1 * * * ?"
    # 任务锁配置
    task-lock:
      # 是否启用任务锁统计
      enable-statistics: true
      # 任务超时时间（毫秒），超过此时间的任务将被认为可能存在问题
      task-timeout-ms: 1800000  # 30分钟
      # 是否记录任务锁详细日志
      enable-detailed-logging: true
